{"name": "agenda-familiar", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@capacitor/android": "7.2.0", "@capacitor/app": "7.0.1", "@capacitor/browser": "^7.0.1", "@capacitor/core": "7.2.0", "@capacitor/haptics": "7.0.1", "@capacitor/ios": "7.2.0", "@capacitor/keyboard": "7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/status-bar": "7.0.1", "@ionic/react": "^8.0.0", "@ionic/react-router": "^8.0.0", "@tailwindcss/vite": "^4.1.8", "@types/styled-components": "^5.1.34", "ionicons": "^7.2.2", "local-ssl-proxy": "^2.0.5", "oidc-client-ts": "^3.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "^5.3.4", "react-router-dom": "^5.3.4", "styled-components": "^6.1.18", "swiper": "^11.2.8"}, "devDependencies": {"@capacitor/cli": "7.2.0", "@tailwindcss/forms": "^0.5.10", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-legacy": "^5.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "typescript": "^5.2.2", "vite": "^5.0.8"}}