import React from 'react';
import { <PERSON>Button, IonAlert } from '@ionic/react';
import { <PERSON>rowser } from '@capacitor/browser';
import { Capacitor } from '@capacitor/core';
import { App } from '@capacitor/app';

const TestBrowser: React.FC = () => {
  const [showAlert, setShowAlert] = React.useState(false);
  const [alertMessage, setAlertMessage] = React.useState('');

  const testBrowser = async () => {
    try {
      console.log('🧪 [TEST] Iniciando test del Browser plugin...');
      console.log('🧪 [TEST] Plataforma:', Capacitor.getPlatform());
      console.log('🧪 [TEST] Es nativo:', Capacitor.isNativePlatform());

      if (Capacitor.isNativePlatform()) {
        // Configurar listener para deep links
        const handleAppUrlOpen = (event: any) => {
          console.log('🧪 [TEST] Deep link recibido:', event.url);
          setAlertMessage(`Deep link recibido: ${event.url}`);
          setShowAlert(true);
          App.removeAllListeners();
          Browser.close();
        };

        App.addListener('appUrlOpen', handleAppUrlOpen);

        // Abrir una URL de prueba que redirige a nuestro scheme
        const testUrl = 'https://httpbin.org/redirect-to?url=capacitor://localhost/test';
        
        console.log('🧪 [TEST] Abriendo URL:', testUrl);
        await Browser.open({
          url: testUrl,
          windowName: '_system'
        });

        // Timeout de 30 segundos
        setTimeout(() => {
          App.removeAllListeners();
          Browser.close();
          setAlertMessage('Timeout - No se recibió deep link');
          setShowAlert(true);
        }, 30000);

      } else {
        // En web, simplemente abrir una ventana
        window.open('https://www.google.com', '_blank');
        setAlertMessage('Browser abierto en web');
        setShowAlert(true);
      }
    } catch (error) {
      console.error('🧪 [TEST] Error:', error);
      setAlertMessage(`Error: ${error}`);
      setShowAlert(true);
    }
  };

  return (
    <>
      <IonButton onClick={testBrowser} color="warning" expand="block">
        🧪 Test Browser Plugin
      </IonButton>

      <IonAlert
        isOpen={showAlert}
        onDidDismiss={() => setShowAlert(false)}
        header="Test Result"
        message={alertMessage}
        buttons={['OK']}
      />
    </>
  );
};

export default TestBrowser;
