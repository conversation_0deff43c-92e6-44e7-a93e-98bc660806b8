import React, { useState } from 'react';
import { IonButton, IonIcon, IonSpinner, IonText, IonCard, IonCardContent } from '@ionic/react';
import { logIn, shield } from 'ionicons/icons';
import { useOIDCUser } from '../contexts/OIDCUserContext';
import { debugLog } from '../config/environment.config';

/**
 * OpenID Connect Login Button Component
 * Provides seamless authentication with Santillana Connect
 */

interface OIDCLoginButtonProps {
  className?: string;
  size?: 'small' | 'default' | 'large';
  fill?: 'clear' | 'outline' | 'solid';
  expand?: 'block' | 'full';
  disabled?: boolean;
  onLoginStart?: () => void;
  onLoginSuccess?: () => void;
  onLoginError?: (error: Error) => void;
}

export const OIDCLoginButton: React.FC<OIDCLoginButtonProps> = ({
  className = '',
  size = 'default',
  fill = 'solid',
  expand,
  disabled = false,
  onLoginStart,
  onLoginSuccess,
  onLoginError
}) => {
  const { login, isLoading } = useOIDCUser();
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleLogin = async () => {
    if (isAuthenticating || isLoading || disabled) {
      return;
    }

    try {
      debugLog('OIDCLoginButton - Starting authentication');
      setIsAuthenticating(true);
      setError(null);
      
      // Notify parent component
      onLoginStart?.();

      // Perform OIDC authentication
      await login();

      debugLog('OIDCLoginButton - Authentication successful');
      onLoginSuccess?.();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Authentication failed';
      console.error('❌ OIDCLoginButton - Authentication failed:', error);
      
      setError(errorMessage);
      onLoginError?.(error instanceof Error ? error : new Error(errorMessage));
    } finally {
      setIsAuthenticating(false);
    }
  };

  const isButtonDisabled = disabled || isLoading || isAuthenticating;
  const showSpinner = isLoading || isAuthenticating;

  return (
    <div className={`oidc-login-container ${className}`}>
      <IonButton
        onClick={handleLogin}
        disabled={isButtonDisabled}
        size={size}
        fill={fill}
        expand={expand}
        className="oidc-login-button"
      >
        {showSpinner ? (
          <>
            <IonSpinner name="crescent" className="mr-2" />
            <span>Autenticando...</span>
          </>
        ) : (
          <>
            <IonIcon icon={shield} className="mr-2" />
            <span>Iniciar Sesión con Santillana Connect</span>
          </>
        )}
      </IonButton>

      {error && (
        <IonText color="danger" className="mt-2 block text-sm">
          <p>❌ {error}</p>
          <p className="text-xs mt-1">
            Por favor, inténtalo de nuevo o contacta con soporte si el problema persiste.
          </p>
        </IonText>
      )}
    </div>
  );
};

/**
 * Simplified OIDC Login Button for quick integration
 */
export const SimpleOIDCLogin: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <OIDCLoginButton
      className={className}
      expand="block"
      size="large"
      fill="solid"
    />
  );
};

/**
 * OIDC Login Card Component
 * Complete login interface with branding and instructions
 */
export const OIDCLoginCard: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <IonCard className={`oidc-login-card ${className}`}>
      <IonCardContent>
        {/* Header */}
        <div className="auth-header">
          <div className="auth-icon-container">
            <IonIcon
              icon={shield}
              className="auth-main-icon"
              style={{ color: 'var(--ion-color-primary)' }}
            />
          </div>
          <h1 className="auth-title">
            Bienvenido a Agenda Familiar
          </h1>
          <p className="auth-subtitle">
            Inicia sesión con tu cuenta de Santillana Connect para acceder a toda la información de tus hijos.
          </p>
        </div>

        {/* Login Button */}
        <div className="mb-4">
          <SimpleOIDCLogin />
        </div>

        {/* Security Notice */}
        <div className="auth-info">
          <IonText color="medium" className="text-center">
            <p style={{ fontSize: '0.875rem', lineHeight: '1.4' }}>
              🔒 Tu información está protegida con los más altos estándares de seguridad.
              Al iniciar sesión, aceptas nuestros términos de uso y política de privacidad.
            </p>
          </IonText>
        </div>
      </IonCardContent>
    </IonCard>
  );
};

/**
 * OIDC Status Indicator
 * Shows current authentication status
 */
export const OIDCStatusIndicator: React.FC = () => {
  const { isAuthenticated, user, isLoading } = useOIDCUser();

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 text-gray-500">
        <IonSpinner name="dots" />
        <span className="text-sm">Verificando sesión...</span>
      </div>
    );
  }

  if (isAuthenticated && user) {
    return (
      <div className="flex items-center space-x-2 text-green-600">
        <IonIcon icon={shield} />
        <span className="text-sm">Conectado como {user.name}</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2 text-gray-500">
      <IonIcon icon={logIn} />
      <span className="text-sm">No autenticado</span>
    </div>
  );
};

export default OIDCLoginButton;
