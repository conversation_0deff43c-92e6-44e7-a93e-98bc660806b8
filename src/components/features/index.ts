/**
 * Feature Components Exports
 * Components specific to application features
 */

// Attendance Components
export { default as AttendanceCard } from '../AttendanceCard/AttendanceCard';
export { default as AttendanceSection } from '../AttendanceSection/AttendanceSection';

// Curriculum Components
export { default as CurricularProgressCard } from '../CurricularProgressCard/CurricularProgressCard';
export { default as CurriculumProgress } from '../CurriculumProgress/CurriculumProgress';

// Catalog Components
export { default as CatalogoHeader } from '../CatalogoHeader/CatalogoHeader';
export { default as ProductCard } from '../ProductCard/ProductCard';

// Institution Components
export { default as InstitutionCard } from '../InstitutionCard/InstitutionCard';
export { default as WelcomeCard } from '../WelcomeCard/WelcomeCard';

// Communication Components
export { default as RecentMessages } from '../RecentMessages/RecentMessages';
export { default as UpcomingEvents } from '../UpcomingEvents/UpcomingEvents';
