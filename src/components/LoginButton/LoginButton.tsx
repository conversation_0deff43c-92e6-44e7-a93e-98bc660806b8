import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IonAlert } from '@ionic/react';
import { useAuth } from '../../hooks/useAuth';

const LoginButton: React.FC = () => {
  const { login, logout, isAuthenticated, isLoading, error, user } = useAuth();
  const [showAlert, setShowAlert] = React.useState(false);

  React.useEffect(() => {
    if (error) {
      setShowAlert(true);
    }
  }, [error]);

  if (isAuthenticated && user) {
    return (
      <div>
        <p>Bienvenido, {user.name}!</p>
        <IonButton onClick={logout} color="secondary">
          Cerrar Sesión
        </IonButton>
      </div>
    );
  }

  return (
    <>
      <IonButton 
        onClick={login} 
        disabled={isLoading}
        expand="block"
      >
        {isLoading ? (
          <>
            <IonSpinner name="crescent" />
            &nbsp; Iniciando sesión...
          </>
        ) : (
          'Iniciar sesión con Santillana Connect'
        )}
      </IonButton>

      <IonAlert
        isOpen={showAlert}
        onDidDismiss={() => setShowAlert(false)}
        header="Error de autenticación"
        message={error || ''}
        buttons={['OK']}
      />
    </>
  );
};

export default LoginButton;
