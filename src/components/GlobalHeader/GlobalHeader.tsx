import React, { useState } from 'react';
import {
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButton,
  IonIcon,
  IonPopover,
  IonList,
  IonItem,
  IonLabel,
  IonAvatar,
} from '@ionic/react';
import {
  chevronBackOutline,
  chevronDownOutline,
  cartOutline,
  personOutline,
  logOutOutline,
} from 'ionicons/icons';
import { useHistory, useLocation } from 'react-router-dom';
import { useOIDCUser as useUser } from '../../contexts/OIDCUserContext';
import { ROUTES } from '../../routes/routes';
import './GlobalHeader.css';

interface GlobalHeaderProps {
  title?: string;
  showBackButton?: boolean;
  showUserInfo?: boolean;
  showActions?: boolean;
  customActions?: React.ReactNode;
  className?: string;
}

const GlobalHeader: React.FC<GlobalHeaderProps> = ({
  title,
  showBackButton = false,
  showUserInfo = true,
  showActions = true,
  customActions,
  className = '',
}) => {
  const history = useHistory();
  const location = useLocation();
  const { user, selectedStudent, selectStudent, logout } = useUser();
  const [isStudentPopoverOpen, setIsStudentPopoverOpen] = useState(false);

  const handleBackClick = () => {
    if (history.length > 1) {
      history.goBack();
    } else {
      history.replace(ROUTES.TABS.HOME);
    }
  };

  const handleStudentSelect = (student: any) => {
    selectStudent(student);
    setIsStudentPopoverOpen(false);
  };

  const handleCartClick = () => {
    console.log('Cart button clicked');
    // TODO: Navigate to cart page
  };

  const handleProfileClick = () => {
    history.push(ROUTES.TABS.ACCOUNT);
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  // Determine if we should show the catalog-style header
  const isCatalogStyle = location.pathname === ROUTES.TABS.CATALOG;

  return (
    <IonHeader className={`global-header catalog-style ${className}`}>
      <IonToolbar className="global-header-toolbar">
        {/* Standard Ionic Header for non-catalog pages */}
        {!isCatalogStyle && (
          <>
            {showBackButton && (
              <IonButton
                fill="clear"
                slot="start"
                onClick={handleBackClick}
                className="back-button"
                aria-label="Volver"
              >
                <IonIcon icon={chevronBackOutline} />
              </IonButton>
            )}
            
            <IonTitle className="header-title">{title}</IonTitle>
            
            {showActions && (
              <div slot="end" className="header-actions">
                {customActions || (
                  <>
                    <IonButton
                      fill="clear"
                      onClick={handleCartClick}
                      className="header-action-btn"
                      aria-label="Carrito de compras"
                    >
                      <IonIcon icon={cartOutline} />
                    </IonButton>
                    <IonButton
                      fill="clear"
                      onClick={handleProfileClick}
                      className="header-action-btn"
                      aria-label="Perfil de usuario"
                    >
                      <IonIcon icon={personOutline} />
                    </IonButton>
                    <IonButton
                      fill="clear"
                      onClick={handleLogout}
                      className="header-action-btn"
                      aria-label="Cerrar sesión"
                    >
                      <IonIcon icon={logOutOutline} />
                    </IonButton>
                  </>
                )}
              </div>
            )}
          </>
        )}

        {/* Catalog-style header content */}
        {isCatalogStyle && (
          <div className="catalog-header-content">
            <div className="catalog-header-left">
              {showBackButton && (
                <IonButton
                  fill="clear"
                  onClick={handleBackClick}
                  className="catalog-back-button"
                  aria-label="Volver"
                >
                  <IonIcon icon={chevronBackOutline} />
                </IonButton>
              )}

              {showUserInfo && user && selectedStudent && (
                <div className="student-dropdown-container">
                  <IonButton
                    fill="clear"
                    id="student-dropdown-trigger"
                    className="student-dropdown-button"
                    aria-label="Seleccionar estudiante"
                  >
                    <div className="student-info">
                      <IonAvatar className="student-avatar">
                        <img 
                          src={'https://ionicframework.com/docs/img/demos/avatar.svg'} 
                          alt="Avatar del estudiante"
                        />
                      </IonAvatar>
                      <div className="student-details">
                        <div className="student-label">Progreso de</div>
                        <div className="student-name">{selectedStudent.name}</div>
                      </div>
                    </div>
                    <IonIcon icon={chevronDownOutline} className="dropdown-icon" />
                  </IonButton>

                  <IonPopover
                    trigger="student-dropdown-trigger"
                    isOpen={isStudentPopoverOpen}
                    onDidDismiss={() => setIsStudentPopoverOpen(false)}
                    showBackdrop={true}
                  >
                    <IonList>
                      {user.children.map((student: any) => (
                        <IonItem
                          key={student.id}
                          button
                          onClick={() => handleStudentSelect(student)}
                          className={selectedStudent?.id === student.id ? 'selected' : ''}
                        >
                          <IonAvatar slot="start">
                            <img src={'https://ionicframework.com/docs/img/demos/avatar.svg'} alt={student.name} />
                          </IonAvatar>
                          <IonLabel>
                            <h3>{student.name}</h3>
                            <p>{student.grade} - {student.school}</p>
                          </IonLabel>
                        </IonItem>
                      ))}
                    </IonList>
                  </IonPopover>
                </div>
              )}
            </div>

            {showActions && (
              <div className="catalog-header-right">
                {customActions || (
                  <>
                    <IonButton
                      fill="clear"
                      onClick={handleCartClick}
                      className="catalog-action-btn"
                      aria-label="Carrito de compras"
                    >
                      <div className="action-icon-container">
                        <IonIcon icon={cartOutline} />
                      </div>
                    </IonButton>
                    <IonButton
                      fill="clear"
                      onClick={handleProfileClick}
                      className="catalog-action-btn"
                      aria-label="Perfil de usuario"
                    >
                      <div className="action-icon-container">
                        <IonIcon icon={personOutline} />
                      </div>
                    </IonButton>
                    <IonButton
                      fill="clear"
                      onClick={handleLogout}
                      className="catalog-action-btn"
                      aria-label="Cerrar sesión"
                    >
                      <div className="action-icon-container">
                        <IonIcon icon={logOutOutline} />
                      </div>
                    </IonButton>
                  </>
                )}
              </div>
            )}
          </div>
        )}
      </IonToolbar>
    </IonHeader>
  );
};

export default GlobalHeader;
