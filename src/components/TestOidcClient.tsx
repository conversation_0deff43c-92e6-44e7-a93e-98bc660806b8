import React from 'react';
import { IonButton, IonContent, IonItem, IonLabel, IonList } from '@ionic/react';
import { OidcClient } from 'oidc-client-ts';
import { AUTH_CONFIG, getRedirectUri } from '../config/auth';

const TestOidcClient: React.FC = () => {
  const [testResults, setTestResults] = React.useState<string[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testOidcClient = async () => {
    try {
      addResult('🔧 Iniciando test de OidcClient...');
      
      // Crear cliente OIDC
      const authority = 'https://pre-identity.santillanaconnect.com';
      const oidcClient = new OidcClient({
        authority: authority,
        client_id: AUTH_CONFIG.clientId,
        redirect_uri: getRedirectUri(),
        response_type: 'code',
        scope: AUTH_CONFIG.scope,
      });

      addResult('✅ OidcClient creado correctamente');

      // Generar signin request
      const signinRequest = await oidcClient.createSigninRequest({
        state: undefined,
        nonce: undefined,
      });

      addResult('✅ SigninRequest creado correctamente');

      // Extraer parámetros PKCE
      const url = new URL(signinRequest.url);
      const codeChallenge = url.searchParams.get('code_challenge');
      const state = url.searchParams.get('state');
      const codeVerifier = (signinRequest as any).code_verifier;

      addResult(`📋 Code challenge: ${codeChallenge?.substring(0, 20)}...`);
      addResult(`📋 Code verifier: ${codeVerifier?.substring(0, 20)}...`);
      addResult(`📋 State: ${state?.substring(0, 20)}...`);
      addResult(`📋 Code challenge length: ${codeChallenge?.length}`);
      addResult(`📋 Code verifier length: ${codeVerifier?.length}`);

      // Verificar que los parámetros PKCE están presentes
      if (codeChallenge && codeVerifier && state) {
        addResult('✅ PKCE generado automáticamente correctamente');
      } else {
        addResult('❌ Error: Faltan parámetros PKCE');
      }

      addResult('🎯 Test completado');

    } catch (error) {
      addResult(`❌ Error: ${error}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <IonContent>
      <div style={{ padding: '20px' }}>
        <h2>Test OidcClient PKCE</h2>
        
        <IonButton expand="block" onClick={testOidcClient}>
          Probar PKCE Automático
        </IonButton>
        
        <IonButton expand="block" fill="outline" onClick={clearResults}>
          Limpiar Resultados
        </IonButton>

        <IonList>
          {testResults.map((result, index) => (
            <IonItem key={index}>
              <IonLabel>
                <pre style={{ fontSize: '12px', whiteSpace: 'pre-wrap' }}>{result}</pre>
              </IonLabel>
            </IonItem>
          ))}
        </IonList>
      </div>
    </IonContent>
  );
};

export default TestOidcClient;
