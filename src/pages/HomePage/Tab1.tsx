import React from 'react';
import { IonContent, IonPage, IonText, IonButton } from '@ionic/react';
import GlobalHeader from '../../components/GlobalHeader/GlobalHeader';
import WelcomeCard from '../../components/WelcomeCard/WelcomeCard';
import AttendanceCard from '../../components/AttendanceCard/AttendanceCard';
import CurricularProgressCard from '../../components/CurricularProgressCard/CurricularProgressCard';
import { useOIDCUser as useUser } from '../../contexts/OIDCUserContext';
import './Tab1.css';
import { useState, useEffect } from 'react';

interface AttendanceData {
  date: string;
  dayName: string;
  dayNumber: number;
  amStatus: 'present' | 'late' | 'justified' | 'unjustified';
  pmStatus: 'present' | 'late' | 'justified' | 'unjustified';
}

interface ProgressData {
  subject: string;
  experience: string;
  sequence: string;
  completedPercentage: number;
  inProgressPercentage: number;
}

const Tab1: React.FC = () => {
  const { user } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [attendanceData, setAttendanceData] = useState<AttendanceData[]>([]);
  const [progressData, setProgressData] = useState<ProgressData | null>(null);

  // Obtener nombre del usuario para mostrar
  const userName = user?.name || 'Usuario';

  useEffect(() => {
    // Simulate loading data
    const loadData = async () => {
      try {
        // Mock attendance data for last 5 business days
        const mockAttendanceData: AttendanceData[] = [
          { date: '2024-01-12', dayName: 'LUN', dayNumber: 12, amStatus: 'present', pmStatus: 'present' },
          { date: '2024-01-13', dayName: 'MAR', dayNumber: 13, amStatus: 'present', pmStatus: 'unjustified' },
          { date: '2024-01-14', dayName: 'MIE', dayNumber: 14, amStatus: 'present', pmStatus: 'present' },
          { date: '2024-01-15', dayName: 'JUE', dayNumber: 15, amStatus: 'justified', pmStatus: 'late' },
          { date: '2024-01-16', dayName: 'VIE', dayNumber: 16, amStatus: 'present', pmStatus: 'present' },
        ];

        // Mock curricular progress data
        const mockProgressData: ProgressData = {
          subject: 'Matemáticas',
          experience: 'Experiencia N°1: Representar e interpretar números racionales en sus expresiones fracción y decimal para resolver problemas de situaciones aditivas.',
          sequence: 'Secuencia N°2',
          completedPercentage: 60,
          inProgressPercentage: 30,
        };

        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        setAttendanceData(mockAttendanceData);
        setProgressData(mockProgressData);
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  return (
    <IonPage>
      <GlobalHeader title="Inicio" />
      <IonContent fullscreen className="tab1-content">

        <div className="tab1-container">
          <WelcomeCard />

          <div className="cards-section">
            <AttendanceCard
              data={attendanceData}
              isLoading={isLoading}
              month="Enero 2024"
              period="Últimos 5 días hábiles"
            />

            {progressData && (
              <CurricularProgressCard
                data={progressData}
                isLoading={isLoading}
              />
            )}
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default Tab1;
