import React from 'react';
import {
  IonContent,
  IonPage,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonText
} from '@ionic/react';
import LoginButton from '../../components/LoginButton/LoginButton';
import TestBrowser from '../../components/TestBrowser/TestBrowser';

const LoginPage: React.FC = () => {
  return (
    <IonPage>
      <IonContent className="ion-padding">
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          minHeight: '100vh' 
        }}>
          <IonCard style={{ width: '100%', maxWidth: '400px' }}>
            <IonCardHeader>
              <IonCardTitle>Agenda Familiar</IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonText>
                <p>Inicia sesión para acceder a tu agenda familiar</p>
              </IonText>
              <LoginButton />
              <br />
              <TestBrowser />
            </IonCardContent>
          </IonCard>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default LoginPage;
