import React, { useState, useEffect } from 'react';
import {
  IonContent,
  IonPage,
  IonCard,
  IonCardContent,
  IonButton,
  IonIcon,
  IonAlert,
  IonText,
  IonSpinner
} from '@ionic/react';
import {
  logInOutline,
  alertCircleOutline,
  shield,
  checkmarkCircle
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { ROUTES } from '../../routes/routes';
import LoadingButton from '../../components/LoadingButton/LoadingButton';
import { debugLog } from '../../config/environment.config';
import { useOIDCUser } from '../../contexts/OIDCUserContext';
import { OIDCLoginCard, OIDCStatusIndicator } from '../../components/OIDCLoginButton';
// Using OIDC authentication instead of manual OAuth2

const CapacitorAuthPage: React.FC = () => {
  const history = useHistory();
  const { login, isAuthenticated, isLoading, user } = useOIDCUser();
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertType, setAlertType] = useState<'success' | 'error'>('error');

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      debugLog('CapacitorAuthPage - User already authenticated, redirecting to home');
      history.replace(ROUTES.HOME);
    }
  }, [isAuthenticated, user, history]);

  const handleOIDCLogin = async () => {
    if (isAuthenticating || isLoading) return;

    setIsAuthenticating(true);
    setShowAlert(false);

    try {
      debugLog('CapacitorAuthPage - Starting OIDC authentication');
      console.log('📱 [UI] Starting OpenID Connect authentication with Santillana Connect');

      await login();

      console.log('🎉 [UI] OIDC authentication successful');
      debugLog('CapacitorAuthPage - OIDC authentication successful');

      // Navigation will be handled by the useEffect above

    } catch (error: any) {
      console.error('❌ [UI] OIDC authentication failed:', error);
      debugLog('CapacitorAuthPage - OIDC authentication failed', error);

      // Show user-friendly error message
      const errorMessage = error?.message || 'Error de autenticación OIDC. Por favor, inténtalo de nuevo.';
      setAlertMessage(errorMessage);
      setAlertType('error');
      setShowAlert(true);
    } finally {
      setIsAuthenticating(false);
    }
  };

  // Show success state if authenticated
  if (isAuthenticated && user) {
    return (
      <IonPage>
        <IonContent className="auth-content">
          <div className="auth-container">
            <IonCard className="auth-card">
              <IonCardContent className="auth-card-content">
                <div className="auth-header">
                  <div className="auth-icon-container">
                    <IonIcon
                      icon={checkmarkCircle}
                      className="auth-main-icon"
                      style={{ color: 'var(--ion-color-success)' }}
                      aria-hidden="true"
                    />
                  </div>
                  <h1 className="auth-title">
                    ¡Autenticación Exitosa!
                  </h1>
                  <p className="auth-subtitle">
                    Bienvenido, {user.name}. Redirigiendo a la aplicación...
                  </p>
                </div>
                <div className="auth-info">
                  <OIDCStatusIndicator />
                </div>
              </IonCardContent>
            </IonCard>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  return (
    <IonPage>
      <IonContent className="auth-content">
        {/* Main Auth Container */}
        <div className="auth-container">
          <OIDCLoginCard />
        </div>

        {/* Error Alert */}
        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => setShowAlert(false)}
          header={alertType === 'error' ? 'Error de Autenticación OIDC' : 'Éxito'}
          message={alertMessage}
          buttons={[
            {
              text: 'Entendido',
              role: 'cancel',
              handler: () => {
                setShowAlert(false);
              }
            },
            ...(alertType === 'error' ? [{
              text: 'Reintentar',
              handler: () => {
                setShowAlert(false);
                handleOIDCLogin();
              }
            }] : [])
          ]}
        />
      </IonContent>
    </IonPage>
  );
};

export default CapacitorAuthPage;
