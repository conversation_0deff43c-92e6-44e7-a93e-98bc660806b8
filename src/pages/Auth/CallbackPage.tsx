import React, { useEffect } from 'react';
import { IonContent, IonPage, IonSpinner, IonText } from '@ionic/react';
import { useHistory } from 'react-router-dom';

const CallbackPage: React.FC = () => {
  const history = useHistory();

  useEffect(() => {
    // Esta página maneja el callback de OAuth2 en web
    const handleCallback = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get('code');
      const state = urlParams.get('state');
      const error = urlParams.get('error');

      if (error) {
        console.error('🔐 [CALLBACK] Error en OAuth2:', error);
        // Enviar error al opener (ventana padre)
        if (window.opener) {
          window.opener.postMessage({
            type: 'AUTH_ERROR',
            error: error
          }, window.location.origin);
          window.close();
        } else {
          // Si no hay opener, redirigir a login con error
          history.push('/login?error=' + encodeURIComponent(error));
        }
        return;
      }

      if (code && state) {
        console.log('🔐 [CALLBACK] Callback exitoso, código recibido');
        // Enviar éxito al opener (ventana padre)
        if (window.opener) {
          window.opener.postMessage({
            type: 'AUTH_SUCCESS',
            url: window.location.href
          }, window.location.origin);
          window.close();
        } else {
          // Si no hay opener, esto no debería pasar en el flujo normal
          console.warn('🔐 [CALLBACK] No hay ventana padre, redirigiendo a home');
          history.push('/');
        }
      } else {
        console.error('🔐 [CALLBACK] Parámetros de callback inválidos');
        if (window.opener) {
          window.opener.postMessage({
            type: 'AUTH_ERROR',
            error: 'Parámetros de callback inválidos'
          }, window.location.origin);
          window.close();
        } else {
          history.push('/login?error=' + encodeURIComponent('Parámetros de callback inválidos'));
        }
      }
    };

    handleCallback();
  }, [history]);

  return (
    <IonPage>
      <IonContent className="ion-padding">
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column',
          justifyContent: 'center', 
          alignItems: 'center', 
          minHeight: '100vh' 
        }}>
          <IonSpinner name="crescent" />
          <IonText>
            <p>Procesando autenticación...</p>
          </IonText>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default CallbackPage;
