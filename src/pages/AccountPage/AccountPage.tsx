import React from 'react';
import {
  IonContent,
  IonPage,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonList,
  IonItem,
  IonLabel,
  IonIcon,
  IonAvatar,
  IonButton,
  IonChip,
  IonBadge,
  IonGrid,
  IonRow,
  IonCol,
} from '@ionic/react';
import {
  personCircleOutline,
  mailOutline,
  callOutline,
  schoolOutline,
  logOutOutline,
  settingsOutline,
  notificationsOutline,
  helpCircleOutline,
  shieldCheckmarkOutline,
  documentTextOutline,
  chevronForwardOutline,
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { ROUTES } from '../../routes/routes';
import ThemeToggle from '../../components/ThemeToggle/ThemeToggle';
import { useAuth } from '../../hooks/useAuth';
import GlobalHeader from '../../components/GlobalHeader/GlobalHeader';
import './AccountPage.css';

// Interfaz extendida para datos de usuario con información adicional
interface ExtendedUserData {
  name: string;
  email: string;
  phone?: string;
  role?: string;
  avatar?: string;
  memberSince?: string;
  children?: Array<{
    name: string;
    grade: string;
    school: string;
  }>;
  picture?: string;
  sub?: string;
  preferred_username?: string;
}

// Mock de datos adicionales del usuario - En una implementación real, esto vendría de tu API
const getExtendedUserData = (user: any): ExtendedUserData => {
  return {
    name: user?.name || 'Usuario',
    email: user?.email || '<EMAIL>',
    phone: '+34 123 456 789', // Esto vendría de tu API
    role: 'Padre/Madre', // Esto vendría de tu API
    avatar: user?.picture || 'https://ionicframework.com/docs/img/demos/avatar.svg',
    memberSince: '2023', // Esto vendría de tu API
    children: [ // Esto vendría de tu API
      { name: 'María Pérez', grade: '5º Primaria', school: 'Colegio San José' },
      { name: 'Carlos Pérez', grade: '2º ESO', school: 'Colegio San José' }
    ],
    picture: user?.picture,
    sub: user?.sub,
    preferred_username: user?.preferred_username
  };
};

const AccountPage: React.FC = () => {
  const history = useHistory();
  const { user, logout } = useAuth();

  // Get extended user data with additional information
  const userData = getExtendedUserData(user);

  const handleLogout = async () => {
    try {
      await logout();
      // The logout function will handle the redirect via OIDC
    } catch (error) {
      console.error('Error during logout:', error);
      // Fallback redirect if OIDC logout fails
      history.replace(ROUTES.AUTH);
    }
  };

  const handleEditProfile = () => {
    // TODO: Navigate to edit profile page
    console.log('Edit profile');
  };

  const handleNotifications = () => {
    // TODO: Navigate to notifications settings
    console.log('Notifications settings');
  };

  const handleHelp = () => {
    // TODO: Navigate to help page
    console.log('Help & Support');
  };

  const handlePrivacy = () => {
    // TODO: Navigate to privacy settings
    console.log('Privacy & Security');
  };

  const handleTerms = () => {
    // TODO: Navigate to terms page
    console.log('Terms & Conditions');
  };

  return (
    <IonPage>
      <GlobalHeader title="Mi Cuenta" />

      <IonContent className="account-content">
        {/* Profile Header Card */}
        <IonCard className="profile-header-card">
          <IonCardContent className="profile-header-content">
            <div className="profile-main">
              <IonAvatar className="profile-avatar">
                <img src={userData.avatar || '../../../public/assets/images/avatar.png'} alt="Avatar del usuario" />
              </IonAvatar>
              <div className="profile-info">
                <h2 className="profile-name">{userData.name}</h2>
                <IonChip className="role-chip" color="primary">
                  <IonIcon icon={schoolOutline} />
                  <IonLabel>{userData.role || 'Usuario'}</IonLabel>
                </IonChip>
                <p className="member-since">Miembro desde {userData.memberSince || '2024'}</p>
              </div>
              <IonButton
                fill="clear"
                size="small"
                className="edit-profile-btn"
                onClick={handleEditProfile}
              >
                <IonIcon icon={settingsOutline} slot="icon-only" />
              </IonButton>
            </div>
          </IonCardContent>
        </IonCard>

        {/* Children Overview Card */}
        <IonCard className="children-card">
          <IonCardHeader>
            <IonCardTitle className="section-title">
              <IonIcon icon={schoolOutline} className="section-icon" />
              Mis Hijos
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent className="children-content">
            <IonGrid>
              <IonRow>
                {userData.children?.map((child: any, index: number) => (
                  <IonCol size="12" sizeMd="6" key={index}>
                    <div className="child-item">
                      <div className="child-info">
                        <h3 className="child-name">{child.name}</h3>
                        <p className="child-grade">{child.grade}</p>
                        <p className="child-school">{child.school}</p>
                      </div>
                      <IonBadge color="success" className="child-badge">
                        Activo
                      </IonBadge>
                    </div>
                  </IonCol>
                ))}
              </IonRow>
            </IonGrid>
          </IonCardContent>
        </IonCard>

        {/* Contact Information Card */}
        <IonCard className="contact-card">
          <IonCardHeader>
            <IonCardTitle className="section-title">
              <IonIcon icon={personCircleOutline} className="section-icon" />
              Información de Contacto
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonList className="contact-list">
              <IonItem lines="none" className="contact-item">
                <IonIcon icon={mailOutline} slot="start" className="contact-icon" />
                <IonLabel>
                  <h3 className="contact-label">Email</h3>
                  <p className="contact-value">{userData.email}</p>
                </IonLabel>
              </IonItem>

              <IonItem lines="none" className="contact-item">
                <IonIcon icon={callOutline} slot="start" className="contact-icon" />
                <IonLabel>
                  <h3 className="contact-label">Teléfono</h3>
                  <p className="contact-value">{userData.phone || 'No disponible'}</p>
                </IonLabel>
              </IonItem>
            </IonList>
          </IonCardContent>
        </IonCard>

        {/* Settings & Preferences Card */}
        <IonCard className="settings-card">
          <IonCardHeader>
            <IonCardTitle className="section-title">
              <IonIcon icon={settingsOutline} className="section-icon" />
              Configuración
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonList className="settings-list">
              {/* Theme Toggle Integration */}
              <ThemeToggle className="theme-toggle-item" />

              <IonItem button onClick={handleNotifications} className="settings-item">
                <IonIcon icon={notificationsOutline} slot="start" className="settings-icon" />
                <IonLabel>
                  <h3>Notificaciones</h3>
                  <p>Gestionar alertas y recordatorios</p>
                </IonLabel>
                <IonIcon icon={chevronForwardOutline} slot="end" className="chevron-icon" />
              </IonItem>

              <IonItem button onClick={handlePrivacy} className="settings-item">
                <IonIcon icon={shieldCheckmarkOutline} slot="start" className="settings-icon" />
                <IonLabel>
                  <h3>Privacidad y Seguridad</h3>
                  <p>Configurar privacidad de datos</p>
                </IonLabel>
                <IonIcon icon={chevronForwardOutline} slot="end" className="chevron-icon" />
              </IonItem>
            </IonList>
          </IonCardContent>
        </IonCard>

        {/* Support & Legal Card */}
        <IonCard className="support-card">
          <IonCardHeader>
            <IonCardTitle className="section-title">
              <IonIcon icon={helpCircleOutline} className="section-icon" />
              Soporte y Legal
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonList className="support-list">
              <IonItem button onClick={handleHelp} className="support-item">
                <IonIcon icon={helpCircleOutline} slot="start" className="support-icon" />
                <IonLabel>
                  <h3>Ayuda y Soporte</h3>
                  <p>Preguntas frecuentes y contacto</p>
                </IonLabel>
                <IonIcon icon={chevronForwardOutline} slot="end" className="chevron-icon" />
              </IonItem>

              <IonItem button onClick={handleTerms} className="support-item">
                <IonIcon icon={documentTextOutline} slot="start" className="support-icon" />
                <IonLabel>
                  <h3>Términos y Condiciones</h3>
                  <p>Políticas de uso y privacidad</p>
                </IonLabel>
                <IonIcon icon={chevronForwardOutline} slot="end" className="chevron-icon" />
              </IonItem>
            </IonList>
          </IonCardContent>
        </IonCard>

        {/* Logout Section */}
        <div className="logout-section">
          <IonButton
            expand="block"
            fill="outline"
            color="danger"
            onClick={handleLogout}
            className="logout-button"
          >
            <IonIcon slot="start" icon={logOutOutline} />
            Cerrar Sesión
          </IonButton>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default AccountPage; 