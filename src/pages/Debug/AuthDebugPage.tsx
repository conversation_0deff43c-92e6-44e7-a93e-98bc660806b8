import React, { useState } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonItem,
  IonLabel,
  IonList,
  IonTextarea,
  IonGrid,
  IonRow,
  IonCol
} from '@ionic/react';
import { Capacitor } from '@capacitor/core';
import { Preferences } from '@capacitor/preferences';
import { CapacitorAuthService } from '../../services/capacitor-auth.service';

const AuthDebugPage: React.FC = () => {
  const [debugOutput, setDebugOutput] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const addToOutput = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugOutput(prev => `${prev}[${timestamp}] ${message}\n`);
  };

  const clearOutput = () => {
    setDebugOutput('');
  };

  const testAuthState = async () => {
    addToOutput('🔍 Testing authentication state...');
    try {
      const isAuthenticated = await CapacitorAuthService.isAuthenticated();
      addToOutput(`✅ Authentication state: ${isAuthenticated ? 'Authenticated' : 'Not authenticated'}`);
    } catch (error) {
      addToOutput(`❌ Error testing auth state: ${error}`);
    }
  };

  const testCurrentUser = async () => {
    addToOutput('👤 Testing current user...');
    try {
      const user = await CapacitorAuthService.getCurrentUser();
      if (user) {
        addToOutput(`✅ User found: ${user.profile.name || user.profile.email}`);
        addToOutput(`   - User ID: ${user.profile.sub}`);
        addToOutput(`   - Email: ${user.profile.email}`);
        addToOutput(`   - Token expires in: ${user.expiresIn} seconds`);
      } else {
        addToOutput('ℹ️ No current user found');
      }
    } catch (error) {
      addToOutput(`❌ Error getting current user: ${error}`);
    }
  };

  const testSignIn = async () => {
    addToOutput('🚀 Testing sign in...');
    setIsLoading(true);
    try {
      const authResult = await CapacitorAuthService.signIn();
      addToOutput(`✅ Sign in successful: ${authResult.profile.name || authResult.profile.email}`);
    } catch (error) {
      addToOutput(`❌ Sign in failed: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testSignOut = async () => {
    addToOutput('🚪 Testing sign out...');
    try {
      await CapacitorAuthService.signOut();
      addToOutput('✅ Sign out successful');
    } catch (error) {
      addToOutput(`❌ Sign out failed: ${error}`);
    }
  };

  const clearStoredData = async () => {
    addToOutput('🧹 Clearing all stored data...');
    try {
      await Preferences.clear();
      addToOutput('✅ All stored data cleared');
    } catch (error) {
      addToOutput(`❌ Error clearing data: ${error}`);
    }
  };

  const checkStoredKeys = async () => {
    addToOutput('🔑 Checking stored keys...');
    try {
      const keys = await Preferences.keys();
      const authKeys = keys.keys.filter(key => key.startsWith('auth_'));
      if (authKeys.length > 0) {
        addToOutput(`✅ Found ${authKeys.length} auth keys: ${authKeys.join(', ')}`);
      } else {
        addToOutput('ℹ️ No auth keys found');
      }
    } catch (error) {
      addToOutput(`❌ Error checking keys: ${error}`);
    }
  };

  const checkPlatformInfo = () => {
    addToOutput('📱 Platform information:');
    addToOutput(`   - Platform: ${Capacitor.getPlatform()}`);
    addToOutput(`   - Is native: ${Capacitor.isNativePlatform()}`);
    addToOutput(`   - Is plugin available: ${Capacitor.isPluginAvailable('InAppBrowser')}`);
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Authentication Debug</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen>
        <IonGrid>
          <IonRow>
            <IonCol size="12" sizeMd="6">
              <IonCard>
                <IonCardHeader>
                  <IonCardTitle>Debug Actions</IonCardTitle>
                </IonCardHeader>
                <IonCardContent>
                  <IonList>
                    <IonItem>
                      <IonButton 
                        expand="block" 
                        fill="outline" 
                        onClick={checkPlatformInfo}
                      >
                        Check Platform Info
                      </IonButton>
                    </IonItem>
                    <IonItem>
                      <IonButton 
                        expand="block" 
                        fill="outline" 
                        onClick={testAuthState}
                      >
                        Check Auth State
                      </IonButton>
                    </IonItem>
                    <IonItem>
                      <IonButton 
                        expand="block" 
                        fill="outline" 
                        onClick={testCurrentUser}
                      >
                        Check Current User
                      </IonButton>
                    </IonItem>
                    <IonItem>
                      <IonButton 
                        expand="block" 
                        fill="outline" 
                        onClick={checkStoredKeys}
                      >
                        Check Stored Keys
                      </IonButton>
                    </IonItem>
                    <IonItem>
                      <IonButton 
                        expand="block" 
                        color="primary" 
                        onClick={testSignIn}
                        disabled={isLoading}
                      >
                        {isLoading ? 'Signing In...' : 'Test Sign In'}
                      </IonButton>
                    </IonItem>
                    <IonItem>
                      <IonButton 
                        expand="block" 
                        color="secondary" 
                        onClick={testSignOut}
                      >
                        Test Sign Out
                      </IonButton>
                    </IonItem>
                    <IonItem>
                      <IonButton 
                        expand="block" 
                        color="danger" 
                        onClick={clearStoredData}
                      >
                        Clear All Data
                      </IonButton>
                    </IonItem>
                    <IonItem>
                      <IonButton 
                        expand="block" 
                        fill="clear" 
                        onClick={clearOutput}
                      >
                        Clear Output
                      </IonButton>
                    </IonItem>
                  </IonList>
                </IonCardContent>
              </IonCard>
            </IonCol>
            <IonCol size="12" sizeMd="6">
              <IonCard>
                <IonCardHeader>
                  <IonCardTitle>Debug Output</IonCardTitle>
                </IonCardHeader>
                <IonCardContent>
                  <IonTextarea
                    value={debugOutput}
                    readonly
                    rows={20}
                    placeholder="Debug output will appear here..."
                    style={{ fontFamily: 'monospace', fontSize: '12px' }}
                  />
                </IonCardContent>
              </IonCard>
            </IonCol>
          </IonRow>
        </IonGrid>
      </IonContent>
    </IonPage>
  );
};

export default AuthDebugPage;
