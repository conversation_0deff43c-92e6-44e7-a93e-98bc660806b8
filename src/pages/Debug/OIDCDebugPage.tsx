import React, { useState } from 'react';
import {
  IonContent,
  IonHeader,
  IonPage,
  IonTitle,
  IonToolbar,
  IonButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonItem,
  IonLabel,
  IonText,
  IonList,
  IonIcon
} from '@ionic/react';
import { bug, checkmark, close, warning } from 'ionicons/icons';
import { OIDCCapacitorService } from '../../services/oidc-capacitor.service';
import { environmentConfig } from '../../config/environment.config';
import { Capacitor } from '@capacitor/core';

const OIDCDebugPage: React.FC = () => {
  const [output, setOutput] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addToOutput = (message: string) => {
    setOutput(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const clearOutput = () => {
    setOutput([]);
    console.clear();
  };

  const testOIDCConfig = () => {
    addToOutput('🔧 OIDC Configuration:');
    addToOutput(`   - Authority: ${environmentConfig.authority}`);
    addToOutput(`   - Client ID: ${environmentConfig.clientId}`);
    addToOutput(`   - Redirect URI: ${environmentConfig.redirectUri}`);
    addToOutput(`   - Scope: ${environmentConfig.scope}`);
    addToOutput(`   - Debug Auth: ${environmentConfig.debugAuth}`);
    addToOutput(`   - Platform: ${Capacitor.getPlatform()}`);
    addToOutput(`   - Is Native: ${Capacitor.isNativePlatform()}`);
  };

  const testOIDCAuthentication = async () => {
    if (isLoading) return;
    
    setIsLoading(true);
    addToOutput('🚀 Starting OIDC Authentication Test...');
    
    try {
      console.log('=== OIDC DEBUG TEST STARTED ===');
      const result = await OIDCCapacitorService.authenticate();
      addToOutput('✅ Authentication successful!');
      addToOutput(`   - User: ${result.user.name} (${result.user.email})`);
      addToOutput(`   - Access Token: ${result.tokens.access_token.substring(0, 20)}...`);
      addToOutput(`   - ID Token: ${result.tokens.id_token.substring(0, 20)}...`);
    } catch (error) {
      addToOutput('❌ Authentication failed!');
      addToOutput(`   - Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error('OIDC Debug Test Error:', error);
    } finally {
      setIsLoading(false);
      console.log('=== OIDC DEBUG TEST COMPLETED ===');
    }
  };

  const testInAppBrowserAvailability = () => {
    addToOutput('📱 InAppBrowser Plugin Check:');
    addToOutput(`   - Available: ${Capacitor.isPluginAvailable('InAppBrowser')}`);
    addToOutput(`   - Platform: ${Capacitor.getPlatform()}`);
    
    if (Capacitor.getPlatform() === 'web') {
      addToOutput('   ⚠️ Running on web - InAppBrowser may not work as expected');
    }
  };

  const testDeepLinkSetup = () => {
    addToOutput('🔗 Deep Link Configuration:');
    addToOutput(`   - Expected Scheme: capacitor://`);
    addToOutput(`   - Expected Host: localhost`);
    addToOutput(`   - Expected Path: /callback`);
    addToOutput(`   - Full URI: ${environmentConfig.redirectUri}`);
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>OIDC Debug</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent className="ion-padding">
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>
              <IonIcon icon={bug} className="mr-2" />
              OIDC Authentication Debug
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonText color="medium">
              <p>Use these tools to debug OIDC authentication issues.</p>
            </IonText>
          </IonCardContent>
        </IonCard>

        <IonCard>
          <IonCardHeader>
            <IonCardTitle>Debug Actions</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonList>
              <IonItem>
                <IonButton 
                  fill="outline" 
                  onClick={testOIDCConfig}
                  className="mr-2"
                >
                  <IonIcon icon={checkmark} slot="start" />
                  Test Config
                </IonButton>
                <IonButton 
                  fill="outline" 
                  onClick={testInAppBrowserAvailability}
                  className="mr-2"
                >
                  <IonIcon icon={checkmark} slot="start" />
                  Test InAppBrowser
                </IonButton>
                <IonButton 
                  fill="outline" 
                  onClick={testDeepLinkSetup}
                >
                  <IonIcon icon={checkmark} slot="start" />
                  Test Deep Links
                </IonButton>
              </IonItem>
              
              <IonItem>
                <IonButton 
                  color="primary"
                  onClick={testOIDCAuthentication}
                  disabled={isLoading}
                  className="mr-2"
                >
                  <IonIcon icon={warning} slot="start" />
                  {isLoading ? 'Testing...' : 'Test Authentication'}
                </IonButton>
                <IonButton 
                  fill="clear" 
                  color="medium"
                  onClick={clearOutput}
                >
                  <IonIcon icon={close} slot="start" />
                  Clear Output
                </IonButton>
              </IonItem>
            </IonList>
          </IonCardContent>
        </IonCard>

        <IonCard>
          <IonCardHeader>
            <IonCardTitle>Debug Output</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <div style={{ 
              backgroundColor: '#1e1e1e', 
              color: '#ffffff', 
              padding: '1rem', 
              borderRadius: '8px',
              fontFamily: 'monospace',
              fontSize: '0.875rem',
              maxHeight: '400px',
              overflowY: 'auto'
            }}>
              {output.length === 0 ? (
                <IonText color="medium">
                  <p>No debug output yet. Click a test button to start.</p>
                </IonText>
              ) : (
                output.map((line, index) => (
                  <div key={index} style={{ marginBottom: '4px' }}>
                    {line}
                  </div>
                ))
              )}
            </div>
          </IonCardContent>
        </IonCard>

        <IonCard>
          <IonCardHeader>
            <IonCardTitle>Instructions</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonText>
              <ol>
                <li><strong>Test Config:</strong> Verify OIDC configuration values</li>
                <li><strong>Test InAppBrowser:</strong> Check if plugin is available</li>
                <li><strong>Test Deep Links:</strong> Verify redirect URI setup</li>
                <li><strong>Test Authentication:</strong> Perform full OIDC flow</li>
              </ol>
              <p><strong>Note:</strong> Check browser console for detailed logs during authentication.</p>
            </IonText>
          </IonCardContent>
        </IonCard>
      </IonContent>
    </IonPage>
  );
};

export default OIDCDebugPage;
