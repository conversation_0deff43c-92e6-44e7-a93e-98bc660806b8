import { useState, useEffect } from 'react';
import { authService } from '../services/authService';

interface UserInfo {
  sub: string;
  name: string;
  email: string;
  picture?: string;
  preferred_username?: string;
}

interface UseAuthReturn {
  user: UserInfo | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: () => Promise<void>;
  logout: () => void;
  error: string | null;
}

export const useAuth = (): UseAuthReturn => {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isAuthenticated = authService.isAuthenticated();

  useEffect(() => {
    // Verificar si hay una sesión activa al cargar
    if (isAuthenticated) {
      // Aquí podrías cargar la información del usuario desde el token
      // o hacer una llamada al endpoint de userinfo
      const loadUserInfo = async () => {
        try {
          const accessToken = authService.getAccessToken();
          if (accessToken) {
            // Podrías hacer una llamada al userinfo endpoint aquí
            // Por ahora, simplemente marcamos como autenticado
            console.log('Usuario autenticado con token existente');
          }
        } catch (err) {
          console.error('Error al cargar información del usuario:', err);
          authService.logout();
        }
      };
      
      loadUserInfo();
    }
  }, [isAuthenticated]);

  const login = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const userInfo = await authService.login();
      if (userInfo) {
        setUser(userInfo);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    authService.logout();
    setUser(null);
    setError(null);
  };

  return {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    error
  };
};
