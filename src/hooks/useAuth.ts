import { useState, useEffect } from 'react';
import { authService } from '../services/authService';

interface UserInfo {
  sub: string;
  name: string;
  email: string;
  picture?: string;
  preferred_username?: string;
}

interface UseAuthReturn {
  user: UserInfo | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: () => Promise<void>;
  logout: () => Promise<void>;
  error: string | null;
}

export const useAuth = (): UseAuthReturn => {
  const [user, setUser] = useState<UserInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Verificar si hay una sesión activa al cargar
    const checkAuthStatus = async () => {
      try {
        const authenticated = await authService.isAuthenticated();
        setIsAuthenticated(authenticated);

        if (authenticated) {
          const accessToken = await authService.getAccessToken();
          if (accessToken) {
            console.log('Usuario autenticado con token existente');
          }
        }
      } catch (err) {
        console.error('Error al verificar autenticación:', err);
        await authService.logout();
        setIsAuthenticated(false);
      }
    };

    checkAuthStatus();
  }, []);

  const login = async () => {
    console.log('🔐 [useAuth] Iniciando login...');
    setIsLoading(true);
    setError(null);

    try {
      const userInfo = await authService.login();
      console.log('🔐 [useAuth] Login exitoso:', userInfo);
      if (userInfo) {
        setUser(userInfo);
        setIsAuthenticated(true);
      }
    } catch (err) {
      console.error('🔐 [useAuth] Error en login:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error desconocido';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    await authService.logout();
    setUser(null);
    setError(null);
    setIsAuthenticated(false);
  };

  return {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    error
  };
};
