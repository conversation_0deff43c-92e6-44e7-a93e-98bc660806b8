/* For information on how to create your own theme, please see:
http://ionicframework.com/docs/theming/ */
@layer theme, base, components, utilities;
@import "tailwindcss/theme.css" layer(theme) important;
@import "tailwindcss/utilities.css" layer(utilities) important;

@utility container {
  margin-inline: auto;
}

:root {
  --ion-color-primary: #3880ff;
  --ion-color-primary-rgb: 56, 128, 255;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #3171e0;
  --ion-color-primary-tint: #4c8dff;

  --ion-color-secondary: #35A192;
  --ion-color-secondary-rgb: 53, 161, 146;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #2f8e80;
  --ion-color-secondary-tint: #49aa9d;

  --ion-background-color: #ffffff;
  --ion-background-color-rgb: 255, 255, 255;
  --ion-text-color: #000000;
  --ion-text-color-rgb: 0, 0, 0;
  --ion-border-color: #d7d8da;

  /* Enhanced theme variables for better dark mode support */
  --ion-card-background: #ffffff;
  --ion-toolbar-background: #ffffff;
  --ion-item-background: #ffffff;

  /* Transition variables */
  --theme-transition-duration: 0.3s;
  --theme-transition-timing: ease;
}


.ion-color-secondary {
  --ion-color-base: var(--ion-color-secondary);
  --ion-color-base-rgb: var(--ion-color-secondary-rgb);
  --ion-color-contrast: var(--ion-color-secondary-contrast);
  --ion-color-contrast-rgb: var(--ion-color-secondary-contrast-rgb);
  --ion-color-shade: var(--ion-color-secondary-shade);
  --ion-color-tint: var(--ion-color-secondary-tint);
}

/* Dark mode variables */
.ion-palette-dark {
  --ion-card-background: #1e1e1e;
  --ion-toolbar-background: #1f1f1f;
  --ion-item-background: #1e1e1e;

  --ion-background-color: #121212;
  --ion-background-color-rgb: 18, 18, 18;
  --ion-text-color: #ffffff;
  --ion-text-color-rgb: 255, 255, 255;
  --ion-border-color: #333333;

  /* Enhanced color variables for better contrast */
  --ion-color-light: #2a2a2a;
  --ion-color-light-rgb: 42, 42, 42;
  --ion-color-light-contrast: #ffffff;
  --ion-color-light-contrast-rgb: 255, 255, 255;
  --ion-color-light-shade: #252525;
  --ion-color-light-tint: #404040;

  --ion-color-medium: #9d9d9d;
  --ion-color-medium-rgb: 157, 157, 157;
  --ion-color-medium-contrast: #000000;
  --ion-color-medium-contrast-rgb: 0, 0, 0;
  --ion-color-medium-shade: #8a8a8a;
  --ion-color-medium-tint: #a7a7a7;

  --ion-color-dark: #f4f4f4;
  --ion-color-dark-rgb: 244, 244, 244;
  --ion-color-dark-contrast: #000000;
  --ion-color-dark-contrast-rgb: 0, 0, 0;
  --ion-color-dark-shade: #d7d7d7;
  --ion-color-dark-tint: #f5f5f5;

  --ion-color-step-50: #1e1e1e;
  --ion-color-step-100: #2a2a2a;
  --ion-color-step-150: #363636;
  --ion-color-step-200: #414141;
  --ion-color-step-250: #4d4d4d;
  --ion-color-step-300: #595959;
  --ion-color-step-350: #656565;
  --ion-color-step-400: #717171;
  --ion-color-step-450: #7d7d7d;
  --ion-color-step-500: #898989;
  --ion-color-step-550: #949494;
  --ion-color-step-600: #a0a0a0;
  --ion-color-step-650: #acacac;
  --ion-color-step-700: #b8b8b8;
  --ion-color-step-750: #c4c4c4;
  --ion-color-step-800: #d0d0d0;
  --ion-color-step-850: #dbdbdb;
  --ion-color-step-900: #e7e7e7;
  --ion-color-step-950: #f3f3f3;
}

/* Global smooth transitions for theme changes */
* {
  transition:
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
}

/* Disable transitions during theme initialization to prevent flash */
.theme-transitioning,
.theme-transitioning * {
  transition: none !important;
}

/* Enhanced component theming */
ion-card {
  --background: var(--ion-card-background);
  transition:
    background var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
}

ion-toolbar {
  --background: var(--ion-toolbar-background);
  transition: background var(--theme-transition-duration) var(--theme-transition-timing);
}

ion-item {
  --background: var(--ion-item-background);
  transition: background var(--theme-transition-duration) var(--theme-transition-timing);
}

/* Improved focus styles for accessibility */
ion-button:focus,
ion-toggle:focus,
ion-input:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}

/* Authentication Page Styles */
.auth-content {
  --padding-top: 0;
  --padding-bottom: 0;
  --padding-start: 0;
  --padding-end: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-secondary) 100%);
}

.auth-container {
  width: 100%;
  max-width: 400px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.auth-card {
  width: 100%;
  margin: 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border-radius: 16px;
  overflow: hidden;
}

.auth-card-content {
  padding: 2rem;
  text-align: center;
}

.auth-header {
  margin-bottom: 2rem;
}

.auth-icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.auth-main-icon {
  font-size: 4rem;
  color: var(--ion-color-primary);
}

.auth-title {
  font-size: 1.75rem;
  font-weight: bold;
  color: var(--ion-text-color);
  margin: 0 0 0.5rem 0;
}

.auth-subtitle {
  font-size: 1rem;
  color: var(--ion-color-medium);
  margin: 0;
  line-height: 1.5;
}

.auth-info {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--ion-border-color);
}

/* OIDC Login Card Styles */
.oidc-login-card {
  width: 100%;
  background: var(--ion-card-background);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  text-align: center;
}

.oidc-login-container {
  width: 100%;
}

.oidc-login-button {
  --border-radius: 12px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  --padding-start: 24px;
  --padding-end: 24px;
  font-weight: 600;
  font-size: 1rem;
  width: 100%;
  margin: 0;
}

.oidc-login-button ion-icon {
  margin-right: 8px;
  font-size: 1.2rem;
}

/* Dark mode adjustments for auth page */
.ion-palette-dark .auth-content {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.ion-palette-dark .oidc-login-card {
  background: var(--ion-color-step-100);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .auth-container {
    padding: 0.5rem;
  }

  .auth-card-content,
  .oidc-login-card {
    padding: 1.5rem;
  }

  .auth-title {
    font-size: 1.5rem;
  }

  .auth-subtitle {
    font-size: 0.9rem;
  }
}

/* Loading states */
.oidc-login-button ion-spinner {
  margin-right: 8px;
}

/* Error states */
.oidc-login-container ion-text[color="danger"] {
  margin-top: 1rem;
  text-align: left;
  padding: 0.75rem;
  background: rgba(var(--ion-color-danger-rgb), 0.1);
  border-radius: 8px;
  border-left: 4px solid var(--ion-color-danger);
}