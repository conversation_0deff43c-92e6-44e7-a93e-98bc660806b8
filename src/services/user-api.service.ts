import { User, Student } from '../contexts/CapacitorUserContext';
import { debugLog } from '../config/environment.config';

/**
 * Service for fetching user data from Santillana's APIs
 * This is a mock implementation that should be replaced with real API calls
 */
export class UserApiService {
  
  /**
   * Fetch user profile and children data from Santillana's backend
   * @param oidcProfile - The OIDC profile from Santillana Connect
   * @param accessToken - The access token for API calls
   */
  static async fetchUserProfile(oidcProfile: any, accessToken: string): Promise<User> {
    try {
      debugLog('UserApiService - Fetching user profile from Santillana APIs', {
        userId: oidcProfile.sub,
        email: oidcProfile.email,
        hasAccessToken: !!accessToken
      });

      // TODO: Replace with real API calls to Santillana's backend
      // Example API calls that should be implemented:
      // 1. GET /api/users/profile - Get user profile details
      // 2. GET /api/users/children - Get user's children/students
      // 3. GET /api/schools - Get school information
      
      // For now, simulate API call with timeout
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock children data - this should come from real API
      const mockChildren: Student[] = await this.fetchUserChildren(oidcProfile.sub, accessToken);
      
      // Create user object with real OIDC data and mock children
      const user: User = {
        id: oidcProfile.sub || 'unknown',
        name: oidcProfile.name || oidcProfile.preferred_username || 'Usuario',
        email: oidcProfile.email || '<EMAIL>',
        phone: oidcProfile.phone_number || oidcProfile.phone || '+34 123 456 789',
        role: this.extractUserRole(oidcProfile),
        avatar: oidcProfile.picture || 'https://ionicframework.com/docs/img/demos/avatar.svg',
        memberSince: this.extractMemberSince(oidcProfile),
        children: mockChildren
      };

      debugLog('UserApiService - User profile fetched successfully', {
        userName: user.name,
        userEmail: user.email,
        childrenCount: user.children.length
      });

      return user;
    } catch (error) {
      console.error('UserApiService - Error fetching user profile:', error);
      throw new Error('Failed to fetch user profile from Santillana APIs');
    }
  }

  /**
   * Fetch user's children/students from Santillana's backend
   * @param userId - The user ID from OIDC
   * @param accessToken - The access token for API calls
   */
  static async fetchUserChildren(userId: string, accessToken: string): Promise<Student[]> {
    try {
      debugLog('UserApiService - Fetching user children from Santillana APIs', { userId });

      // TODO: Replace with real API call
      // Example: GET /api/users/{userId}/children
      // Headers: { Authorization: `Bearer ${accessToken}` }
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Mock data - this should come from real API response
      const mockChildren: Student[] = [
        { 
          id: '1',
          name: 'María Camila Zuluaga A.', 
          grade: '5º Primaria', 
          school: 'Colegio San José',
          avatar: '/api/placeholder/25/25'
        },
        { 
          id: '2',
          name: 'Carlos Pérez', 
          grade: '2º ESO', 
          school: 'Colegio San José',
          avatar: '/api/placeholder/25/25'
        }
      ];

      debugLog('UserApiService - User children fetched successfully', {
        childrenCount: mockChildren.length
      });

      return mockChildren;
    } catch (error) {
      console.error('UserApiService - Error fetching user children:', error);
      return []; // Return empty array on error
    }
  }

  /**
   * Extract user role from OIDC profile
   * @param oidcProfile - The OIDC profile
   */
  private static extractUserRole(oidcProfile: any): string {
    // Try to extract role from various possible claims
    if (oidcProfile.role) return oidcProfile.role;
    if (oidcProfile.roles && Array.isArray(oidcProfile.roles)) return oidcProfile.roles[0];
    if (oidcProfile.groups && Array.isArray(oidcProfile.groups)) return oidcProfile.groups[0];
    if (oidcProfile['custom:role']) return oidcProfile['custom:role'];
    
    // Default role
    return 'Padre/Madre';
  }

  /**
   * Extract member since date from OIDC profile
   * @param oidcProfile - The OIDC profile
   */
  private static extractMemberSince(oidcProfile: any): string {
    // Try to extract creation date from various possible claims
    if (oidcProfile.created_at) {
      return new Date(oidcProfile.created_at).getFullYear().toString();
    }
    if (oidcProfile.iat) {
      return new Date(oidcProfile.iat * 1000).getFullYear().toString();
    }
    
    // Default to current year
    return new Date().getFullYear().toString();
  }

  /**
   * Update user profile in Santillana's backend
   * @param userId - The user ID
   * @param updates - The profile updates
   * @param accessToken - The access token for API calls
   */
  static async updateUserProfile(userId: string, updates: Partial<User>, accessToken: string): Promise<void> {
    try {
      debugLog('UserApiService - Updating user profile in Santillana APIs', {
        userId,
        updates: Object.keys(updates)
      });

      // TODO: Replace with real API call
      // Example: PUT /api/users/{userId}/profile
      // Headers: { Authorization: `Bearer ${accessToken}` }
      // Body: updates
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      debugLog('UserApiService - User profile updated successfully');
    } catch (error) {
      console.error('UserApiService - Error updating user profile:', error);
      throw new Error('Failed to update user profile in Santillana APIs');
    }
  }
}
