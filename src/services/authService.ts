import { Browser } from '@capacitor/browser';
import { Capacitor, CapacitorHttp } from '@capacitor/core';
import { App } from '@capacitor/app';
import { Preferences } from '@capacitor/preferences';
import { OidcClient } from 'oidc-client-ts';
import { AUTH_CONFIG, getRedirectUri } from '../config/auth';

interface UserInfo {
  sub: string;
  name: string;
  email: string;
  picture?: string;
  preferred_username?: string;
  // Agrega otros campos que devuelva Santillana Connect
}

interface TokenResponse {
  access_token: string;
  id_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
}

class AuthService {
  private oidcClient: OidcClient;

  constructor() {
    // Configurar OidcClient solo para generación de PKCE
    // Extraer authority de los endpoints existentes
    const authority = 'https://pre-identity.santillanaconnect.com';

    this.oidcClient = new OidcClient({
      authority: authority,
      client_id: AUTH_CONFIG.clientId,
      redirect_uri: getRedirectUri(),
      response_type: 'code',
      scope: AUTH_CONFIG.scope,
      // PKCE se habilitará automáticamente
    });
  }

  // Método para generar PKCE usando oidc-client-ts
  private async generatePKCE(): Promise<{ codeVerifier: string; codeChallenge: string; state: string }> {
    // Usar oidc-client-ts para generar PKCE automáticamente
    const signinRequest = await this.oidcClient.createSigninRequest({
      state: undefined, // Dejar que oidc-client-ts genere el state
      nonce: undefined, // Dejar que oidc-client-ts genere el nonce
    });

    // Extraer los parámetros PKCE de la URL generada
    const url = new URL(signinRequest.url);
    const codeChallenge = url.searchParams.get('code_challenge')!;
    const state = url.searchParams.get('state')!;

    // El code_verifier se almacena internamente en oidc-client-ts
    // Lo extraemos del signinRequest
    const codeVerifier = (signinRequest as any).code_verifier;

    console.log('🔐 [AUTH] PKCE generado automáticamente por oidc-client-ts');
    console.log('🔐 [AUTH] Code challenge:', codeChallenge.substring(0, 10) + '...');
    console.log('🔐 [AUTH] Code verifier:', codeVerifier.substring(0, 10) + '...');
    console.log('🔐 [AUTH] State:', state.substring(0, 10) + '...');

    return { codeVerifier, codeChallenge, state };
  }

  async login(): Promise<UserInfo | null> {
    try {
      console.log('🔐 [AUTH] Iniciando proceso de login...');
      console.log('🔐 [AUTH] Plataforma:', Capacitor.getPlatform());
      console.log('🔐 [AUTH] Es nativo:', Capacitor.isNativePlatform());

      // Generar PKCE automáticamente usando oidc-client-ts
      const { codeVerifier, codeChallenge, state } = await this.generatePKCE();

      // Guardar en Preferences para verificar después
      await Preferences.set({ key: 'codeVerifier', value: codeVerifier });
      await Preferences.set({ key: 'authState', value: state });

      console.log('🔐 [AUTH] Code verifier guardado en Preferences:', codeVerifier.substring(0, 10) + '...');
      const savedVerifier = await Preferences.get({ key: 'codeVerifier' });
      console.log('🔐 [AUTH] Verificando Preferences:', savedVerifier.value?.substring(0, 10) + '...');

      // Construir URL de autorización
      const authUrl = new URL(AUTH_CONFIG.authorizationEndpoint);
      authUrl.searchParams.append('client_id', AUTH_CONFIG.clientId);
      authUrl.searchParams.append('redirect_uri', getRedirectUri());
      authUrl.searchParams.append('response_type', 'code');
      authUrl.searchParams.append('scope', AUTH_CONFIG.scope);
      authUrl.searchParams.append('state', state);
      authUrl.searchParams.append('code_challenge', codeChallenge);
      authUrl.searchParams.append('code_challenge_method', 'S256');

      console.log('🔐 [AUTH] URL de autorización:', authUrl.toString());
      console.log('🔐 [AUTH] Redirect URI:', getRedirectUri());

      if (Capacitor.isNativePlatform()) {
        console.log('🔐 [AUTH] Ejecutando flujo nativo...');
        // En plataforma nativa, usar Browser plugin
        return new Promise(async (resolve, reject) => {
          // Configurar listener para deep links antes de abrir el browser
          const handleAppUrlOpen = async (event: any) => {
            const url = event.url;
            console.log('🔐 [AUTH] Deep link recibido:', url);

            const expectedRedirectUri = getRedirectUri();
            console.log('🔐 [AUTH] Esperando redirect URI:', expectedRedirectUri);

            if (url.startsWith(expectedRedirectUri)) {
              // Remover listener y cerrar browser
              App.removeAllListeners();
              await Browser.close();

              try {
                const result = await this.handleCallback(url);
                resolve(result);
              } catch (error) {
                reject(error);
              }
            }
          };

          // Agregar listener para deep links
          App.addListener('appUrlOpen', handleAppUrlOpen);

          // Timeout después de 5 minutos
          const timeoutId = setTimeout(() => {
            App.removeAllListeners();
            Browser.close();
            reject(new Error('Timeout de autenticación'));
          }, 300000);

          try {
            // Abrir browser para autenticación
            console.log('🔐 [AUTH] Intentando abrir browser con URL:', authUrl.toString());
            await Browser.open({
              url: authUrl.toString(),
              windowName: '_system'
            });
            console.log('🔐 [AUTH] Browser abierto exitosamente para autenticación');
          } catch (error) {
            console.error('🔐 [AUTH] Error al abrir browser:', error);
            clearTimeout(timeoutId);
            App.removeAllListeners();
            reject(error);
          }
        });
      } else {
        // En web, usar ventana popup
        const popup = window.open(
          authUrl.toString(),
          'auth',
          'width=500,height=600,scrollbars=yes,resizable=yes'
        );

        return new Promise((resolve, reject) => {
          const checkClosed = setInterval(() => {
            if (popup?.closed) {
              clearInterval(checkClosed);
              reject(new Error('Ventana de autenticación cerrada'));
            }
          }, 1000);

          // Escuchar mensajes del popup
          const messageListener = (event: MessageEvent) => {
            if (event.origin !== window.location.origin) return;
            
            if (event.data.type === 'AUTH_SUCCESS') {
              clearInterval(checkClosed);
              popup?.close();
              window.removeEventListener('message', messageListener);
              this.handleCallback(event.data.url).then(resolve).catch(reject);
            }
          };

          window.addEventListener('message', messageListener);
        });
      }
    } catch (error) {
      console.error('Error en login:', error);
      return null;
    }
  }

  private async handleCallback(callbackUrl: string): Promise<UserInfo> {
    const url = new URL(callbackUrl);
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');
    const error = url.searchParams.get('error');

    if (error) {
      throw new Error(`Error de autenticación: ${error}`);
    }

    if (!code || !state) {
      throw new Error('Parámetros de callback inválidos');
    }

    // Verificar state
    const savedStateResult = await Preferences.get({ key: 'authState' });
    const savedState = savedStateResult.value;
    console.log('🔐 [AUTH] State recibido:', state);
    console.log('🔐 [AUTH] State guardado:', savedState);

    if (state !== savedState) {
      throw new Error('Estado de autenticación inválido');
    }

    // Intercambiar código por tokens
    const tokenResponse = await this.exchangeCodeForTokens(code);
    
    // Obtener información del usuario
    const userInfo = await this.getUserInfo(tokenResponse.access_token);
    
    // Guardar tokens
    await Preferences.set({ key: 'accessToken', value: tokenResponse.access_token });
    await Preferences.set({ key: 'idToken', value: tokenResponse.id_token });
    if (tokenResponse.refresh_token) {
      await Preferences.set({ key: 'refreshToken', value: tokenResponse.refresh_token });
    }
    
    // Limpiar datos temporales
    await Preferences.remove({ key: 'codeVerifier' });
    await Preferences.remove({ key: 'authState' });

    return userInfo;
  }

  private async exchangeCodeForTokens(code: string): Promise<TokenResponse> {
    const codeVerifierResult = await Preferences.get({ key: 'codeVerifier' });
    const codeVerifier = codeVerifierResult.value;
    console.log('🔐 [AUTH] Code verifier recuperado de Preferences:', codeVerifier?.substring(0, 10) + '...');
    console.log('🔐 [AUTH] Code verifier recuperado length:', codeVerifier?.length);

    if (!codeVerifier) {
      throw new Error('Code verifier no encontrado');
    }

    const body = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: AUTH_CONFIG.clientId,
      code: code,
      redirect_uri: getRedirectUri(),
      code_verifier: codeVerifier
    });

    console.log('🔐 [AUTH] Intercambiando código por tokens...');
    console.log('🔐 [AUTH] Token endpoint:', AUTH_CONFIG.tokenEndpoint);
    console.log('🔐 [AUTH] Redirect URI:', getRedirectUri());
    console.log('🔐 [AUTH] Code verifier length:', codeVerifier.length);
    console.log('🔐 [AUTH] Authorization code:', code.substring(0, 10) + '...');
    console.log('🔐 [AUTH] Body params:', body.toString());

    try {
      const response = await CapacitorHttp.post({
        url: AUTH_CONFIG.tokenEndpoint,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: body.toString()
      });

      console.log('🔐 [AUTH] Respuesta del token endpoint:', response.status);

      if (response.status !== 200) {
        console.error('🔐 [AUTH] Error en token endpoint:', response.data);
        throw new Error(`Error al intercambiar código por tokens: ${response.data}`);
      }

      return response.data;
    } catch (error) {
      console.error('🔐 [AUTH] Error en CapacitorHttp:', error);
      throw error;
    }
  }

  private async getUserInfo(accessToken: string): Promise<UserInfo> {
    console.log('🔐 [AUTH] Obteniendo información del usuario...');

    try {
      const response = await CapacitorHttp.get({
        url: AUTH_CONFIG.userInfoEndpoint,
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      console.log('🔐 [AUTH] Respuesta del userinfo endpoint:', response.status);

      if (response.status !== 200) {
        console.error('🔐 [AUTH] Error en userinfo endpoint:', response.data);
        throw new Error('Error al obtener información del usuario');
      }

      console.log('🔐 [AUTH] Información del usuario obtenida exitosamente');
      return response.data;
    } catch (error) {
      console.error('🔐 [AUTH] Error en getUserInfo:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    await Preferences.remove({ key: 'accessToken' });
    await Preferences.remove({ key: 'idToken' });
    await Preferences.remove({ key: 'refreshToken' });
  }

  async isAuthenticated(): Promise<boolean> {
    const token = await Preferences.get({ key: 'accessToken' });
    return !!token.value;
  }

  async getAccessToken(): Promise<string | null> {
    const token = await Preferences.get({ key: 'accessToken' });
    return token.value;
  }

  async getIdToken(): Promise<string | null> {
    const token = await Preferences.get({ key: 'idToken' });
    return token.value;
  }
}

export const authService = new AuthService();
