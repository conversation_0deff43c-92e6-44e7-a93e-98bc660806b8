import { <PERSON><PERSON><PERSON> } from '@capacitor/browser';
import { Capacitor } from '@capacitor/core';
import { AUTH_CONFIG, getRedirectUri } from '../config/auth';

interface UserInfo {
  sub: string;
  name: string;
  email: string;
  picture?: string;
  preferred_username?: string;
  // Agrega otros campos que devuelva Santillana Connect
}

interface TokenResponse {
  access_token: string;
  id_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
}

class AuthService {
  private generateRandomString(length: number): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(36)).join('').substr(0, length);
  }

  private async sha256(plain: string): Promise<ArrayBuffer> {
    const encoder = new TextEncoder();
    const data = encoder.encode(plain);
    return await crypto.subtle.digest('SHA-256', data);
  }

  private base64URLEncode(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let str = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      str += String.fromCharCode(bytes[i]);
    }
    return btoa(str)
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  async login(): Promise<UserInfo | null> {
    try {
      // Generar PKCE challenge
      const codeVerifier = this.generateRandomString(128);
      const codeChallenge = this.base64URLEncode(await this.sha256(codeVerifier));
      const state = this.generateRandomString(32);

      // Guardar en sessionStorage para verificar después
      sessionStorage.setItem('codeVerifier', codeVerifier);
      sessionStorage.setItem('authState', state);

      // Construir URL de autorización
      const authUrl = new URL(AUTH_CONFIG.authorizationEndpoint);
      authUrl.searchParams.append('client_id', AUTH_CONFIG.clientId);
      authUrl.searchParams.append('redirect_uri', getRedirectUri());
      authUrl.searchParams.append('response_type', 'code');
      authUrl.searchParams.append('scope', AUTH_CONFIG.scope);
      authUrl.searchParams.append('state', state);
      authUrl.searchParams.append('code_challenge', codeChallenge);
      authUrl.searchParams.append('code_challenge_method', 'S256');

      if (Capacitor.isNativePlatform()) {
        // En plataforma nativa, usar Browser plugin
        const result = await Browser.open({
          url: authUrl.toString(),
          windowName: '_system'
        });

        // Escuchar el callback
        return new Promise((resolve, reject) => {
          const handleUrl = (event: any) => {
            const url = event.url;
            if (url.startsWith(AUTH_CONFIG.redirectUri)) {
              Browser.close();
              this.handleCallback(url).then(resolve).catch(reject);
            }
          };

          Browser.addListener('browserPageLoaded', handleUrl);
          
          // Timeout después de 5 minutos
          setTimeout(() => {
            Browser.removeAllListeners();
            reject(new Error('Timeout de autenticación'));
          }, 300000);
        });
      } else {
        // En web, usar ventana popup
        const popup = window.open(
          authUrl.toString(),
          'auth',
          'width=500,height=600,scrollbars=yes,resizable=yes'
        );

        return new Promise((resolve, reject) => {
          const checkClosed = setInterval(() => {
            if (popup?.closed) {
              clearInterval(checkClosed);
              reject(new Error('Ventana de autenticación cerrada'));
            }
          }, 1000);

          // Escuchar mensajes del popup
          const messageListener = (event: MessageEvent) => {
            if (event.origin !== window.location.origin) return;
            
            if (event.data.type === 'AUTH_SUCCESS') {
              clearInterval(checkClosed);
              popup?.close();
              window.removeEventListener('message', messageListener);
              this.handleCallback(event.data.url).then(resolve).catch(reject);
            }
          };

          window.addEventListener('message', messageListener);
        });
      }
    } catch (error) {
      console.error('Error en login:', error);
      return null;
    }
  }

  private async handleCallback(callbackUrl: string): Promise<UserInfo> {
    const url = new URL(callbackUrl);
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');
    const error = url.searchParams.get('error');

    if (error) {
      throw new Error(`Error de autenticación: ${error}`);
    }

    if (!code || !state) {
      throw new Error('Parámetros de callback inválidos');
    }

    // Verificar state
    const savedState = sessionStorage.getItem('authState');
    if (state !== savedState) {
      throw new Error('Estado de autenticación inválido');
    }

    // Intercambiar código por tokens
    const tokenResponse = await this.exchangeCodeForTokens(code);
    
    // Obtener información del usuario
    const userInfo = await this.getUserInfo(tokenResponse.access_token);
    
    // Guardar tokens
    localStorage.setItem('accessToken', tokenResponse.access_token);
    localStorage.setItem('idToken', tokenResponse.id_token);
    if (tokenResponse.refresh_token) {
      localStorage.setItem('refreshToken', tokenResponse.refresh_token);
    }
    
    // Limpiar datos temporales
    sessionStorage.removeItem('codeVerifier');
    sessionStorage.removeItem('authState');

    return userInfo;
  }

  private async exchangeCodeForTokens(code: string): Promise<TokenResponse> {
    const codeVerifier = sessionStorage.getItem('codeVerifier');
    if (!codeVerifier) {
      throw new Error('Code verifier no encontrado');
    }

    const body = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: AUTH_CONFIG.clientId,
      code: code,
      redirect_uri: getRedirectUri(),
      code_verifier: codeVerifier
    });

    const response = await fetch(AUTH_CONFIG.tokenEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: body.toString()
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Error al intercambiar código por tokens: ${errorText}`);
    }

    return await response.json();
  }

  private async getUserInfo(accessToken: string): Promise<UserInfo> {
    const response = await fetch(AUTH_CONFIG.userInfoEndpoint, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    if (!response.ok) {
      throw new Error('Error al obtener información del usuario');
    }

    return await response.json();
  }

  logout(): void {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('idToken');
    localStorage.removeItem('refreshToken');
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('accessToken');
  }

  getAccessToken(): string | null {
    return localStorage.getItem('accessToken');
  }

  getIdToken(): string | null {
    return localStorage.getItem('idToken');
  }
}

export const authService = new AuthService();
