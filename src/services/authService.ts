import { <PERSON>rowser } from '@capacitor/browser';
import { Capacitor, CapacitorHttp } from '@capacitor/core';
import { App } from '@capacitor/app';
import { Preferences } from '@capacitor/preferences';
import { AUTH_CONFIG, getRedirectUri } from '../config/auth';

interface UserInfo {
  sub: string;
  name: string;
  email: string;
  picture?: string;
  preferred_username?: string;
  // Agrega otros campos que devuelva Santillana Connect
}

interface TokenResponse {
  access_token: string;
  id_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
}

class AuthService {
  // Generar string aleatorio base64url-safe para PKCE según RFC 7636
  private generateCodeVerifier(): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    const array = new Uint8Array(43); // RFC 7636: mínimo 43 caracteres
    crypto.getRandomValues(array);
    return Array.from(array, byte => charset[byte % charset.length]).join('');
  }

  // Generar code challenge usando una implementación SHA-256 compatible con iOS
  private async generateCodeChallenge(verifier: string): Promise<string> {
    // Implementación SHA-256 que funciona sin crypto.subtle
    const hash = await this.sha256(verifier);
    return this.base64URLEncode(hash);
  }

  // Implementación SHA-256 compatible con iOS (sin crypto.subtle)
  private async sha256(message: string): Promise<ArrayBuffer> {
    // Implementación simple de SHA-256 para compatibilidad móvil
    function rightRotate(value: number, amount: number): number {
      return (value >>> amount) | (value << (32 - amount));
    }

    function sha256Hash(message: string): number[] {
      const h = [
        0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a,
        0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19
      ];

      const k = [
        0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
        0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
        0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
        0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
        0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
        0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
        0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
        0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
      ];

      // Pre-processing
      const msgBytes = new TextEncoder().encode(message);
      const msgBits = msgBytes.length * 8;
      const msgArray = Array.from(msgBytes);

      // Append bit '1'
      msgArray.push(0x80);

      // Append zeros
      while ((msgArray.length % 64) !== 56) {
        msgArray.push(0);
      }

      // Append length
      for (let i = 7; i >= 0; i--) {
        msgArray.push((msgBits >>> (i * 8)) & 0xff);
      }

      // Process message in 512-bit chunks
      for (let chunk = 0; chunk < msgArray.length; chunk += 64) {
        const w = new Array(64);

        // Copy chunk into first 16 words
        for (let i = 0; i < 16; i++) {
          w[i] = (msgArray[chunk + i * 4] << 24) |
                 (msgArray[chunk + i * 4 + 1] << 16) |
                 (msgArray[chunk + i * 4 + 2] << 8) |
                 msgArray[chunk + i * 4 + 3];
        }

        // Extend the first 16 words into the remaining 48 words
        for (let i = 16; i < 64; i++) {
          const s0 = rightRotate(w[i - 15], 7) ^ rightRotate(w[i - 15], 18) ^ (w[i - 15] >>> 3);
          const s1 = rightRotate(w[i - 2], 17) ^ rightRotate(w[i - 2], 19) ^ (w[i - 2] >>> 10);
          w[i] = (w[i - 16] + s0 + w[i - 7] + s1) >>> 0;
        }

        // Initialize working variables
        let [a, b, c, d, e, f, g, h_val] = h;

        // Main loop
        for (let i = 0; i < 64; i++) {
          const S1 = rightRotate(e, 6) ^ rightRotate(e, 11) ^ rightRotate(e, 25);
          const ch = (e & f) ^ (~e & g);
          const temp1 = (h_val + S1 + ch + k[i] + w[i]) >>> 0;
          const S0 = rightRotate(a, 2) ^ rightRotate(a, 13) ^ rightRotate(a, 22);
          const maj = (a & b) ^ (a & c) ^ (b & c);
          const temp2 = (S0 + maj) >>> 0;

          h_val = g;
          g = f;
          f = e;
          e = (d + temp1) >>> 0;
          d = c;
          c = b;
          b = a;
          a = (temp1 + temp2) >>> 0;
        }

        // Add this chunk's hash to result
        h[0] = (h[0] + a) >>> 0;
        h[1] = (h[1] + b) >>> 0;
        h[2] = (h[2] + c) >>> 0;
        h[3] = (h[3] + d) >>> 0;
        h[4] = (h[4] + e) >>> 0;
        h[5] = (h[5] + f) >>> 0;
        h[6] = (h[6] + g) >>> 0;
        h[7] = (h[7] + h_val) >>> 0;
      }

      return h;
    }

    const hashArray = sha256Hash(message);
    const bytes = new Uint8Array(32);

    for (let i = 0; i < 8; i++) {
      bytes[i * 4] = (hashArray[i] >>> 24) & 0xff;
      bytes[i * 4 + 1] = (hashArray[i] >>> 16) & 0xff;
      bytes[i * 4 + 2] = (hashArray[i] >>> 8) & 0xff;
      bytes[i * 4 + 3] = hashArray[i] & 0xff;
    }

    return bytes.buffer;
  }

  // Codificación base64url según RFC 7636
  private base64URLEncode(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let str = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      str += String.fromCharCode(bytes[i]);
    }
    return btoa(str)
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  // Generar state aleatorio
  private generateState(): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => charset[byte % charset.length]).join('');
  }

  // Método principal para generar PKCE
  private async generatePKCE(): Promise<{ codeVerifier: string; codeChallenge: string; state: string }> {
    const codeVerifier = this.generateCodeVerifier();
    const codeChallenge = await this.generateCodeChallenge(codeVerifier);
    const state = this.generateState();

    console.log('🔐 [AUTH] PKCE generado correctamente (compatible iOS)');
    console.log('🔐 [AUTH] Code verifier length:', codeVerifier.length);
    console.log('🔐 [AUTH] Code challenge length:', codeChallenge.length);
    console.log('🔐 [AUTH] Code verifier:', codeVerifier.substring(0, 10) + '...');
    console.log('🔐 [AUTH] Code challenge:', codeChallenge.substring(0, 10) + '...');
    console.log('🔐 [AUTH] State:', state.substring(0, 10) + '...');

    // Verificación PKCE: regenerar challenge desde verifier para validar
    console.log('🔐 [AUTH] === VERIFICACIÓN PKCE ===');
    const challengeVerification = await this.generateCodeChallenge(codeVerifier);
    console.log('🔐 [AUTH] Challenge original:', codeChallenge);
    console.log('🔐 [AUTH] Challenge verificación:', challengeVerification);
    console.log('🔐 [AUTH] Challenges coinciden:', codeChallenge === challengeVerification ? '✅' : '❌');

    return { codeVerifier, codeChallenge, state };
  }

  async login(): Promise<UserInfo | null> {
    try {
      console.log('🔐 [AUTH] Iniciando proceso de login...');
      console.log('🔐 [AUTH] Plataforma:', Capacitor.getPlatform());
      console.log('🔐 [AUTH] Es nativo:', Capacitor.isNativePlatform());

      // Generar PKCE automáticamente usando oidc-client-ts
      const { codeVerifier, codeChallenge, state } = await this.generatePKCE();

      // Guardar en Preferences para verificar después
      await Preferences.set({ key: 'codeVerifier', value: codeVerifier });
      await Preferences.set({ key: 'authState', value: state });

      console.log('🔐 [AUTH] Code verifier guardado en Preferences:', codeVerifier.substring(0, 10) + '...');
      const savedVerifier = await Preferences.get({ key: 'codeVerifier' });
      console.log('🔐 [AUTH] Verificando Preferences:', savedVerifier.value?.substring(0, 10) + '...');

      // Construir URL de autorización
      const authUrl = new URL(AUTH_CONFIG.authorizationEndpoint);
      authUrl.searchParams.append('client_id', AUTH_CONFIG.clientId);
      authUrl.searchParams.append('redirect_uri', getRedirectUri());
      authUrl.searchParams.append('response_type', 'code');
      authUrl.searchParams.append('scope', AUTH_CONFIG.scope);
      authUrl.searchParams.append('state', state);
      authUrl.searchParams.append('code_challenge', codeChallenge);
      authUrl.searchParams.append('code_challenge_method', 'S256');

      console.log('🔐 [AUTH] URL de autorización:', authUrl.toString());
      console.log('🔐 [AUTH] Redirect URI:', getRedirectUri());

      if (Capacitor.isNativePlatform()) {
        console.log('🔐 [AUTH] Ejecutando flujo nativo...');
        // En plataforma nativa, usar Browser plugin
        return new Promise(async (resolve, reject) => {
          // Configurar listener para deep links antes de abrir el browser
          const handleAppUrlOpen = async (event: any) => {
            const url = event.url;
            console.log('🔐 [AUTH] Deep link recibido:', url);

            const expectedRedirectUri = getRedirectUri();
            console.log('🔐 [AUTH] Esperando redirect URI:', expectedRedirectUri);

            if (url.startsWith(expectedRedirectUri)) {
              // Remover listener y cerrar browser
              App.removeAllListeners();
              await Browser.close();

              try {
                const result = await this.handleCallback(url);
                resolve(result);
              } catch (error) {
                reject(error);
              }
            }
          };

          // Agregar listener para deep links
          App.addListener('appUrlOpen', handleAppUrlOpen);

          // Timeout después de 5 minutos
          const timeoutId = setTimeout(() => {
            App.removeAllListeners();
            Browser.close();
            reject(new Error('Timeout de autenticación'));
          }, 300000);

          try {
            // Abrir browser para autenticación
            console.log('🔐 [AUTH] Intentando abrir browser con URL:', authUrl.toString());
            await Browser.open({
              url: authUrl.toString(),
              windowName: '_system'
            });
            console.log('🔐 [AUTH] Browser abierto exitosamente para autenticación');
          } catch (error) {
            console.error('🔐 [AUTH] Error al abrir browser:', error);
            clearTimeout(timeoutId);
            App.removeAllListeners();
            reject(error);
          }
        });
      } else {
        // En web, usar ventana popup
        const popup = window.open(
          authUrl.toString(),
          'auth',
          'width=500,height=600,scrollbars=yes,resizable=yes'
        );

        return new Promise((resolve, reject) => {
          const checkClosed = setInterval(() => {
            if (popup?.closed) {
              clearInterval(checkClosed);
              reject(new Error('Ventana de autenticación cerrada'));
            }
          }, 1000);

          // Escuchar mensajes del popup
          const messageListener = (event: MessageEvent) => {
            if (event.origin !== window.location.origin) return;
            
            if (event.data.type === 'AUTH_SUCCESS') {
              clearInterval(checkClosed);
              popup?.close();
              window.removeEventListener('message', messageListener);
              this.handleCallback(event.data.url).then(resolve).catch(reject);
            }
          };

          window.addEventListener('message', messageListener);
        });
      }
    } catch (error) {
      console.error('Error en login:', error);
      return null;
    }
  }

  private async handleCallback(callbackUrl: string): Promise<UserInfo> {
    console.log('🔐 [AUTH] === INICIANDO HANDLE CALLBACK ===');
    console.log('🔐 [AUTH] Callback URL recibida:', callbackUrl);

    try {
      const url = new URL(callbackUrl);
      const code = url.searchParams.get('code');
      const state = url.searchParams.get('state');
      const error = url.searchParams.get('error');

      console.log('🔐 [AUTH] Parámetros extraídos:');
      console.log('🔐 [AUTH] - Code:', code ? code.substring(0, 10) + '...' : 'null');
      console.log('🔐 [AUTH] - State:', state ? state.substring(0, 10) + '...' : 'null');
      console.log('🔐 [AUTH] - Error:', error || 'null');

      if (error) {
        console.error('🔐 [AUTH] Error en callback:', error);
        throw new Error(`Error de autenticación: ${error}`);
      }

      if (!code || !state) {
        console.error('🔐 [AUTH] Parámetros de callback faltantes');
        throw new Error('Parámetros de callback inválidos');
      }

      // Verificar state
      console.log('🔐 [AUTH] Verificando state...');
      const savedStateResult = await Preferences.get({ key: 'authState' });
      const savedState = savedStateResult.value;
      console.log('🔐 [AUTH] State recibido:', state);
      console.log('🔐 [AUTH] State guardado:', savedState);

      if (state !== savedState) {
        console.error('🔐 [AUTH] State mismatch!');
        throw new Error('Estado de autenticación inválido');
      }

      console.log('🔐 [AUTH] State verificado correctamente ✅');

      // Intercambiar código por tokens
      console.log('🔐 [AUTH] === INICIANDO INTERCAMBIO DE TOKENS ===');
      const tokenResponse = await this.exchangeCodeForTokens(code);
      console.log('🔐 [AUTH] Tokens obtenidos exitosamente ✅');

      // Obtener información del usuario
      console.log('🔐 [AUTH] === OBTENIENDO INFO DEL USUARIO ===');
      const userInfo = await this.getUserInfo(tokenResponse.access_token);
      console.log('🔐 [AUTH] Info del usuario obtenida ✅');

      // Guardar tokens
      console.log('🔐 [AUTH] === GUARDANDO TOKENS ===');
      await Preferences.set({ key: 'accessToken', value: tokenResponse.access_token });
      console.log('🔐 [AUTH] Access token guardado ✅');

      await Preferences.set({ key: 'idToken', value: tokenResponse.id_token });
      console.log('🔐 [AUTH] ID token guardado ✅');

      if (tokenResponse.refresh_token) {
        await Preferences.set({ key: 'refreshToken', value: tokenResponse.refresh_token });
        console.log('🔐 [AUTH] Refresh token guardado ✅');
      }

      // Limpiar datos temporales
      console.log('🔐 [AUTH] === LIMPIANDO DATOS TEMPORALES ===');
      await Preferences.remove({ key: 'codeVerifier' });
      await Preferences.remove({ key: 'authState' });
      console.log('🔐 [AUTH] Datos temporales limpiados ✅');

      console.log('🔐 [AUTH] === CALLBACK COMPLETADO EXITOSAMENTE ===');
      return userInfo;

    } catch (error) {
      console.error('🔐 [AUTH] === ERROR EN HANDLE CALLBACK ===');
      console.error('🔐 [AUTH] Error details:', error);
      console.error('🔐 [AUTH] Error message:', error instanceof Error ? error.message : 'Unknown error');
      console.error('🔐 [AUTH] Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      throw error;
    }
  }

  private async exchangeCodeForTokens(code: string): Promise<TokenResponse> {
    console.log('🔐 [AUTH] --- EXCHANGE CODE FOR TOKENS START ---');

    try {
      // Recuperar code verifier
      console.log('🔐 [AUTH] Recuperando code verifier de Preferences...');
      const codeVerifierResult = await Preferences.get({ key: 'codeVerifier' });
      const codeVerifier = codeVerifierResult.value;

      console.log('🔐 [AUTH] Code verifier result:', codeVerifierResult);
      console.log('🔐 [AUTH] Code verifier recuperado:', codeVerifier ? codeVerifier.substring(0, 10) + '...' : 'null');
      console.log('🔐 [AUTH] Code verifier length:', codeVerifier?.length || 0);

      if (!codeVerifier) {
        console.error('🔐 [AUTH] Code verifier no encontrado en Preferences!');
        throw new Error('Code verifier no encontrado');
      }

      // Verificación PKCE: regenerar challenge desde el verifier recuperado
      console.log('🔐 [AUTH] === VERIFICACIÓN PKCE EN TOKEN EXCHANGE ===');
      const regeneratedChallenge = await this.generateCodeChallenge(codeVerifier);
      console.log('🔐 [AUTH] Challenge regenerado desde verifier:', regeneratedChallenge);
      console.log('🔐 [AUTH] Verifier completo:', codeVerifier);

      // Preparar parámetros del request
      const redirectUri = getRedirectUri();
      const body = new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: AUTH_CONFIG.clientId,
        code: code,
        redirect_uri: redirectUri,
        code_verifier: codeVerifier
      });

      console.log('🔐 [AUTH] Parámetros del token request:');
      console.log('🔐 [AUTH] - Token endpoint:', AUTH_CONFIG.tokenEndpoint);
      console.log('🔐 [AUTH] - Grant type: authorization_code');
      console.log('🔐 [AUTH] - Client ID:', AUTH_CONFIG.clientId);
      console.log('🔐 [AUTH] - Redirect URI:', redirectUri);
      console.log('🔐 [AUTH] - Authorization code:', code.substring(0, 10) + '...');
      console.log('🔐 [AUTH] - Code verifier length:', codeVerifier.length);
      console.log('🔐 [AUTH] - Body params:', body.toString());

      // Realizar request HTTP
      console.log('🔐 [AUTH] Enviando request HTTP POST...');
      const response = await CapacitorHttp.post({
        url: AUTH_CONFIG.tokenEndpoint,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        },
        data: body.toString()
      });

      console.log('🔐 [AUTH] Respuesta HTTP recibida:');
      console.log('🔐 [AUTH] - Status:', response.status);
      console.log('🔐 [AUTH] - Headers:', response.headers);
      console.log('🔐 [AUTH] - Data type:', typeof response.data);
      console.log('🔐 [AUTH] - Data preview:', JSON.stringify(response.data).substring(0, 200) + '...');

      if (response.status !== 200) {
        console.error('🔐 [AUTH] Token endpoint error response:');
        console.error('🔐 [AUTH] - Status:', response.status);
        console.error('🔐 [AUTH] - Data:', response.data);
        throw new Error(`Error al intercambiar código por tokens: Status ${response.status}, Data: ${JSON.stringify(response.data)}`);
      }

      // Validar respuesta
      const tokenData = response.data;
      if (!tokenData.access_token) {
        console.error('🔐 [AUTH] Token response missing access_token:', tokenData);
        throw new Error('Respuesta de tokens inválida: falta access_token');
      }

      console.log('🔐 [AUTH] Tokens obtenidos exitosamente:');
      console.log('🔐 [AUTH] - Access token present:', !!tokenData.access_token);
      console.log('🔐 [AUTH] - ID token present:', !!tokenData.id_token);
      console.log('🔐 [AUTH] - Refresh token present:', !!tokenData.refresh_token);
      console.log('🔐 [AUTH] - Token type:', tokenData.token_type);
      console.log('🔐 [AUTH] - Expires in:', tokenData.expires_in);

      console.log('🔐 [AUTH] --- EXCHANGE CODE FOR TOKENS SUCCESS ---');
      return tokenData;

    } catch (error) {
      console.error('🔐 [AUTH] --- EXCHANGE CODE FOR TOKENS ERROR ---');
      console.error('🔐 [AUTH] Error type:', typeof error);
      console.error('🔐 [AUTH] Error message:', error instanceof Error ? error.message : 'Unknown error');
      console.error('🔐 [AUTH] Error details:', error);

      if (error instanceof Error && error.stack) {
        console.error('🔐 [AUTH] Error stack:', error.stack);
      }

      throw error;
    }
  }

  private async getUserInfo(accessToken: string): Promise<UserInfo> {
    console.log('🔐 [AUTH] --- GET USER INFO START ---');
    console.log('🔐 [AUTH] UserInfo endpoint:', AUTH_CONFIG.userInfoEndpoint);
    console.log('🔐 [AUTH] Access token present:', !!accessToken);
    console.log('🔐 [AUTH] Access token preview:', accessToken ? accessToken.substring(0, 20) + '...' : 'null');

    try {
      const response = await CapacitorHttp.get({
        url: AUTH_CONFIG.userInfoEndpoint,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json'
        }
      });

      console.log('🔐 [AUTH] UserInfo response:');
      console.log('🔐 [AUTH] - Status:', response.status);
      console.log('🔐 [AUTH] - Headers:', response.headers);
      console.log('🔐 [AUTH] - Data type:', typeof response.data);

      if (response.status !== 200) {
        console.error('🔐 [AUTH] UserInfo endpoint error:');
        console.error('🔐 [AUTH] - Status:', response.status);
        console.error('🔐 [AUTH] - Data:', response.data);
        throw new Error(`Error al obtener información del usuario: Status ${response.status}`);
      }

      const userInfo = response.data;
      console.log('🔐 [AUTH] User info obtenida:');
      console.log('🔐 [AUTH] - Sub:', userInfo.sub);
      console.log('🔐 [AUTH] - Name:', userInfo.name);
      console.log('🔐 [AUTH] - Email:', userInfo.email);
      console.log('🔐 [AUTH] --- GET USER INFO SUCCESS ---');

      return userInfo;
    } catch (error) {
      console.error('🔐 [AUTH] --- GET USER INFO ERROR ---');
      console.error('🔐 [AUTH] Error details:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    await Preferences.remove({ key: 'accessToken' });
    await Preferences.remove({ key: 'idToken' });
    await Preferences.remove({ key: 'refreshToken' });
  }

  async isAuthenticated(): Promise<boolean> {
    const token = await Preferences.get({ key: 'accessToken' });
    return !!token.value;
  }

  async getAccessToken(): Promise<string | null> {
    const token = await Preferences.get({ key: 'accessToken' });
    return token.value;
  }

  async getIdToken(): Promise<string | null> {
    const token = await Preferences.get({ key: 'idToken' });
    return token.value;
  }
}

export const authService = new AuthService();
