import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";

// Removed old OIDC configuration - now using Capacitor-native authentication

// Global error handling
window.addEventListener('error', (event) => {
  console.error('❌ Global error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('❌ Unhandled promise rejection:', event.reason);
  event.preventDefault();
});

// Initialize debug configuration for Chrome DevTools remote debugging
import { DebugConfig } from "./config/debug.config";
import { Capacitor } from '@capacitor/core';
DebugConfig.initialize();

// Test console logging and crypto for remote debugging
if (Capacitor.isNativePlatform()) {
  console.log('🔧 [MAIN] Testing console logging for Chrome DevTools remote debugging');
  console.log('🔧 [MAIN] Debug configuration initialized for mobile platform');

  // Test crypto functionality early
  import('./utils/crypto').then(({ CryptoUtils }) => {
    const cryptoInfo = CryptoUtils.getCryptoInfo();
    console.log('✅ [MAIN] Crypto functionality available:', cryptoInfo);
  }).catch((error) => {
    console.error('❌ [MAIN] Crypto functionality test failed during startup:', error);
  });

  // Test HTTP functionality early
  import('./utils/http').then(({ HttpService }) => {
    console.log('✅ [MAIN] HTTP service initialized');
  }).catch((error) => {
    console.warn('⚠️ [MAIN] HTTP service initialization failed:', error);
  });
}

// Startup information
console.log("✅ [MAIN] Starting Agenda Familiar App with Capacitor-native authentication");

ReactDOM.createRoot(document.getElementById("root")!).render(
  <App />
);