import React, { useEffect } from 'react';
import { Redirect, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { ROUTES } from './routes';

const RouteGuard: React.FC<React.PropsWithChildren> = ({ children }) => {
  const location = useLocation();
  const { isAuthenticated, isLoading, user } = useAuth();
  const hasSeenWelcome = localStorage.getItem('hasSeenWelcome') === 'true';

  useEffect(() => {
    console.log('RouteGuard - Route change', {
      pathname: location.pathname,
      hasSeenWelcome,
      isAuthenticated,
      isLoading,
      hasUser: !!user,
      userEmail: user?.email
    });
  }, [location.pathname, hasSeenWelcome, isAuthenticated, isLoading, user]);

  // Show loading while authentication is initializing
  if (isLoading) {
    console.log('RouteGuard - Loading state, waiting for authentication');
    return null; // or a loading spinner
  }

  // Si el usuario no ha visto la bienvenida, mostrar los slides
  if (!hasSeenWelcome && location.pathname !== ROUTES.WELCOME) {
    console.log('RouteGuard - User has not seen welcome, redirecting to welcome');
    return <Redirect to={ROUTES.WELCOME} />;
  }

  // Si el usuario ha visto la bienvenida pero no está autenticado
  if (hasSeenWelcome && !isAuthenticated && location.pathname !== ROUTES.AUTH) {
    console.log('RouteGuard - User not authenticated, redirecting to auth', {
      isAuthenticated,
      hasUser: !!user
    });
    return <Redirect to={ROUTES.AUTH} />;
  }

  // Usuario autenticado - permitir acceso
  console.log('RouteGuard - User authenticated, allowing access', {
    userName: user?.name,
    userEmail: user?.email
  });

  return <>{children}</>;
};

export default RouteGuard;
