import React from "react";
import { IonRouterOutlet, IonTabs, IonTabBar, IonTabButton, IonIcon, IonLabel } from '@ionic/react';
import { home as homeIcon, square, triangle } from 'ionicons/icons';
import { Route, Redirect } from "react-router-dom";
import RouteGuard from "./RouteGuard";
import { ROUTES } from './routes';
import WelcomeSlides from '../pages/WelcomeSlides/WelcomeSlides';
import LoginPage from '../pages/Auth/LoginPage';
import CallbackPage from '../pages/Auth/CallbackPage';
import Tab1 from "../pages/HomePage/Tab1";
import Tab2 from "../pages/HomePage/Tab2";
import Tab3 from "../pages/HomePage/Tab3";
import CatalogoTab from "../pages/HomePage/CatalogoTab";
import AccountPage from "../pages/AccountPage/AccountPage";



const AppRoutes: React.FC = () => {
  return (
    <IonRouterOutlet>
      {/* R<PERSON><PERSON> públicas */}
      <Route exact path={ROUTES.WELCOME} component={WelcomeSlides} />

      {/* Authentication routes */}
      <Route exact path={ROUTES.AUTH} component={LoginPage} />

      {/* Callback route for web OAuth2 */}
      <Route exact path="/callback" component={CallbackPage} />

      {/* Protected routes - require authentication */}
      <Route path={ROUTES.HOME}>
        <RouteGuard>
          <IonTabs>
            <IonRouterOutlet>
              {/* Tab sub-routes */}
              <Route exact path={ROUTES.TABS.HOME} component={Tab1} />
              <Route exact path={ROUTES.TABS.REPORTS} component={Tab2} />
              <Route exact path={ROUTES.TABS.CONNECTION} component={Tab3} />
              <Route exact path={ROUTES.TABS.RESOURCES} component={Tab3} />
              <Route exact path={ROUTES.TABS.CATALOG} component={CatalogoTab} />
              <Route exact path={ROUTES.TABS.ACCOUNT} component={AccountPage} />

              {/* Default tab redirect */}
              <Route exact path={ROUTES.HOME}>
                <Redirect to={ROUTES.TABS.HOME} />
              </Route>
            </IonRouterOutlet>

            {/* Tab Bar */}
            <IonTabBar slot="bottom">
              <IonTabButton tab="inicio" href={ROUTES.TABS.HOME}>
                <IonIcon icon={homeIcon} />
                <IonLabel>Inicio</IonLabel>
              </IonTabButton>

              <IonTabButton tab="informes" href={ROUTES.TABS.REPORTS}>
                <IonIcon icon={square} />
                <IonLabel>Informes</IonLabel>
              </IonTabButton>

              <IonTabButton tab="conexion" href={ROUTES.TABS.CONNECTION}>
                <IonIcon icon={triangle} />
                <IonLabel>Conexión</IonLabel>
              </IonTabButton>

              <IonTabButton tab="recursos" href={ROUTES.TABS.RESOURCES}>
                <IonIcon icon={triangle} />
                <IonLabel>Recursos</IonLabel>
              </IonTabButton>

              <IonTabButton tab="catalogo" href={ROUTES.TABS.CATALOG}>
                <IonIcon icon={square} />
                <IonLabel>Catálogo</IonLabel>
              </IonTabButton>

              <IonTabButton tab="cuenta" href={ROUTES.TABS.ACCOUNT}>
                <IonIcon icon={homeIcon} />
                <IonLabel>Cuenta</IonLabel>
              </IonTabButton>
            </IonTabBar>
          </IonTabs>
        </RouteGuard>
      </Route>

      {/* Ruta por defecto */}
      <Route exact path="/">
        <Redirect to={ROUTES.HOME} />
      </Route>
    </IonRouterOutlet>
  );
};

export default AppRoutes;
