import { Capacitor } from "@capacitor/core";

// Environment detection
export const isNative = Capacitor.getPlatform() !== "web";
export const isDev = import.meta.env.DEV;
export const platform = Capacitor.getPlatform();

// Consolidated environment configuration for Capacitor-native authentication
export const environmentConfig = {
  // Santillana Connect OIDC Provider (Pre-production)
  authority: import.meta.env.VITE_OIDC_AUTHORITY || "https://pre-identity.santillanaconnect.com",
  clientId: import.meta.env.VITE_OIDC_CLIENT_ID || "sumun_office_co_pre",
  scope: import.meta.env.VITE_OIDC_SCOPE || "openid profile email offline_access neds/full_access",

  // Redirect URI - Santillana Connect allows capacitor://localhost/callback
  // Confirmed working redirect URI for mobile authentication
  redirectUri: import.meta.env.VITE_REDIRECT_URI || "capacitor://localhost/callback",

  // Debug settings
  debugAuth: import.meta.env.VITE_DEBUG_AUTH === "true" || isDev
} as const;

// Debug logging helper (deprecated - use console.log directly)
// export const debugLog = (message: string, data?: any) => {
//   if (environmentConfig.debugAuth) {
//     console.log(`🔐 [AUTH] ${message}`, data || "");
//   }
// };
