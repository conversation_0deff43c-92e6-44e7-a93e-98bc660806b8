/**
 * Simplified debug configuration for development and production
 * Provides essential debugging capabilities without complex console forwarding
 */

import { Capacitor } from '@capacitor/core';
import { environmentConfig } from './environment.config';

export class DebugConfig {
  private static initialized = false;

  /**
   * Initialize debug configuration
   */
  static initialize(): void {
    if (this.initialized) return;

    // Only initialize debug features in development or when explicitly enabled
    if (environmentConfig.debugAuth) {
      this.logPlatformInfo();
    }

    this.initialized = true;
  }

  /**
   * Log platform and configuration information
   */
  private static logPlatformInfo(): void {
    console.log('🔧 [DEBUG] Debug configuration initialized');
    console.log('🔧 [DEBUG] Platform:', Capacitor.getPlatform());
    console.log('🔧 [DEBUG] Is native platform:', Capacitor.isNativePlatform());
    console.log('🔧 [DEBUG] Available plugins:', this.getAvailablePlugins());
  }



  /**
   * Get list of available Capacitor plugins
   */
  private static getAvailablePlugins(): string[] {
    const plugins = [
      'App',
      'InAppBrowser',
      'Preferences',
      'Device',
      'Network',
      'StatusBar',
      'SplashScreen'
    ];

    return plugins.filter(plugin => Capacitor.isPluginAvailable(plugin));
  }

  /**
   * Log authentication flow start for debugging
   */
  static logAuthFlowStart(): void {
    console.log('🔐 [DEBUG] ==========================================');
    console.log('🔐 [DEBUG] AUTHENTICATION FLOW STARTING');
    console.log('🔐 [DEBUG] ==========================================');
    console.log('🔐 [DEBUG] Timestamp:', new Date().toISOString());
    console.log('🔐 [DEBUG] Platform:', Capacitor.getPlatform());
    console.log('🔐 [DEBUG] User Agent:', navigator.userAgent);
    console.log('🔐 [DEBUG] ==========================================');
  }

  /**
   * Log authentication flow end for debugging
   */
  static logAuthFlowEnd(success: boolean, result?: any): void {
    console.log('🔐 [DEBUG] ==========================================');
    console.log(`🔐 [DEBUG] AUTHENTICATION FLOW ${success ? 'COMPLETED' : 'FAILED'}`);
    console.log('🔐 [DEBUG] ==========================================');
    console.log('🔐 [DEBUG] Timestamp:', new Date().toISOString());
    if (result) {
      console.log('🔐 [DEBUG] Result:', result);
    }
    console.log('🔐 [DEBUG] ==========================================');
  }

  /**
   * Test platform capabilities for debugging
   */
  static testPlatformCapabilities(): void {
    if (!environmentConfig.debugAuth) return;

    console.log('🧪 [TEST] Platform capabilities:', {
      platform: Capacitor.getPlatform(),
      isNative: Capacitor.isNativePlatform(),
      hasCrypto: !!(window.crypto),
      hasSubtle: !!(window.crypto && window.crypto.subtle),
      hasFetch: !!(window.fetch),
      timestamp: new Date().toISOString()
    });
  }
}

// Auto-initialize when module is loaded
DebugConfig.initialize();
