import { Capacitor } from '@capacitor/core';

// Configuración de autenticación para Santillana Connect
export const AUTH_CONFIG = {
  // Endpoints de Santillana Connect (Pre-producción)
  authorizationEndpoint: 'https://pre-identity.santillanaconnect.com/connect/authorize',
  tokenEndpoint: 'https://pre-identity.santillanaconnect.com/connect/token',
  userInfoEndpoint: 'https://pre-identity.santillanaconnect.com/connect/userinfo',
  
  // Configuración del cliente
  clientId: 'sumun_office_co_pre',
  scope: 'openid profile email offline_access neds/full_access',
  
  // Redirect URI - Santillana Connect permite capacitor://localhost/callback
  redirectUri: 'capacitor://localhost/callback',
  
  // Configuración adicional
  responseType: 'code',
  codeChallengeMethod: 'S256'
} as const;

/**
 * Obtiene la URI de redirección apropiada según la plataforma
 */
export const getRedirectUri = (): string => {
  if (Capacitor.isNativePlatform()) {
    return AUTH_CONFIG.redirectUri;
  } else {
    // Para web, usar localhost con el puerto del desarrollo
    return `${window.location.origin}/callback`;
  }
};

/**
 * Configuración de debug
 */
export const DEBUG_AUTH = import.meta.env.VITE_DEBUG_AUTH === 'true' || import.meta.env.DEV;
