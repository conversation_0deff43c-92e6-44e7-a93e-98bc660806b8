/**
 * Crypto Utilities
 * Consolidated cryptographic functionality
 * Combines functionality from crypto-fallback.ts
 */

/**
 * Cryptographic utilities with fallback implementations
 */
export class CryptoUtils {
  /**
   * Generate SHA256 hash of input string
   */
  static async sha256(input: string): Promise<ArrayBuffer> {
    const encoder = new TextEncoder();
    const data = encoder.encode(input);

    // Debug crypto availability
    const hasCrypto = typeof crypto !== 'undefined';
    const hasSubtle = hasCrypto && typeof crypto.subtle !== 'undefined';

    console.log('🔐 [CRYPTO] Crypto availability check', {
      hasCrypto,
      hasSubtle,
      platform: typeof window !== 'undefined' ? 'web' : 'native'
    });

    if (hasSubtle) {
      try {
        // Use Web Crypto API if available
        const result = await crypto.subtle.digest('SHA-256', data);
        console.log('✅ [CRYPTO] Using secure Web Crypto API');
        return result;
      } catch (error) {
        console.warn('⚠️ [CRYPTO] Web Crypto API failed, falling back', error);
        return Promise.resolve(this.sha256Fallback(input));
      }
    } else {
      // Fallback implementation
      console.log('⚠️ [CRYPTO] Using fallback implementation');
      return Promise.resolve(this.sha256Fallback(input));
    }
  }

  /**
   * Base64 URL encode (RFC 4648 Section 5)
   */
  static base64URLEncode(buffer: ArrayBuffer | string): string {
    let base64: string;
    
    if (typeof buffer === 'string') {
      base64 = btoa(buffer);
    } else {
      const bytes = new Uint8Array(buffer);
      const binary = Array.from(bytes, byte => String.fromCharCode(byte)).join('');
      base64 = btoa(binary);
    }
    
    return base64
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  /**
   * Base64 URL decode
   */
  static base64URLDecode(input: string): ArrayBuffer {
    // Add padding if needed
    let padded = input;
    while (padded.length % 4) {
      padded += '=';
    }
    
    // Replace URL-safe characters
    const base64 = padded.replace(/-/g, '+').replace(/_/g, '/');
    
    // Decode
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    
    return bytes.buffer;
  }

  /**
   * Generate random string for PKCE code verifier
   */
  static generateCodeVerifier(length: number = 128): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    let result = '';
    
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      const values = new Uint8Array(length);
      crypto.getRandomValues(values);
      
      for (let i = 0; i < length; i++) {
        result += charset[values[i] % charset.length];
      }
    } else {
      // Fallback using Math.random
      for (let i = 0; i < length; i++) {
        result += charset[Math.floor(Math.random() * charset.length)];
      }
    }
    
    return result;
  }

  /**
   * Generate PKCE code challenge from verifier
   */
  static async generateCodeChallenge(verifier: string): Promise<string> {
    const hash = await this.sha256(verifier);
    const challenge = this.base64URLEncode(hash);

    // Debug the generated challenge
    console.log('🔐 [CRYPTO] PKCE code challenge generated', {
      verifierLength: verifier.length,
      hashByteLength: hash.byteLength,
      challengeLength: challenge.length,
      challengePrefix: challenge.substring(0, 10) + '...'
    });

    return challenge;
  }

  /**
   * Generate random state parameter
   */
  static generateState(length: number = 32): string {
    return this.generateCodeVerifier(length);
  }

  /**
   * Generate random nonce
   */
  static generateNonce(length: number = 32): string {
    return this.generateCodeVerifier(length);
  }

  /**
   * Fallback SHA256 implementation using a simple but functional hash
   * Note: This is not cryptographically secure but produces consistent 32-byte output
   */
  private static sha256Fallback(input: string): ArrayBuffer {
    console.warn('Using fallback SHA256 implementation - not cryptographically secure');

    // Create a 32-byte hash using multiple rounds of simple hashing
    const buffer = new ArrayBuffer(32);
    const view = new Uint8Array(buffer);

    // Initialize with different seeds for each byte position
    for (let i = 0; i < 32; i++) {
      let hash = i * 0x9e3779b9; // Golden ratio based seed

      // Hash the input string with position-specific modifications
      for (let j = 0; j < input.length; j++) {
        const char = input.charCodeAt(j);
        hash = ((hash << 5) - hash) + char + (i * j);
        hash = hash & 0xffffffff; // Keep as 32-bit
      }

      // Additional mixing
      hash ^= hash >>> 16;
      hash *= 0x85ebca6b;
      hash ^= hash >>> 13;
      hash *= 0xc2b2ae35;
      hash ^= hash >>> 16;

      view[i] = hash & 0xff;
    }

    return buffer;
  }

  /**
   * Validate that crypto operations are available
   */
  static isSecureCryptoAvailable(): boolean {
    return typeof crypto !== 'undefined' && 
           typeof crypto.subtle !== 'undefined' && 
           typeof crypto.getRandomValues !== 'undefined';
  }

  /**
   * Get crypto implementation info
   */
  static getCryptoInfo(): { secure: boolean; webCrypto: boolean; randomValues: boolean } {
    return {
      secure: this.isSecureCryptoAvailable(),
      webCrypto: typeof crypto !== 'undefined' && typeof crypto.subtle !== 'undefined',
      randomValues: typeof crypto !== 'undefined' && typeof crypto.getRandomValues !== 'undefined'
    };
  }
}

// Legacy exports for backward compatibility
export const CryptoFallback = CryptoUtils;
