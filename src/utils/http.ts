/**
 * HTTP Utilities
 * Consolidated HTTP functionality for both web and native platforms
 * Combines functionality from native-http.ts
 */

import { CapacitorHttp, HttpOptions, HttpResponse } from '@capacitor/core';
import { Capacitor } from '@capacitor/core';

export interface HttpRequestOptions {
  headers?: Record<string, string>;
  timeout?: number;
}

export interface HttpResponseData {
  data: any;
  status: number;
  headers: Record<string, string>;
  ok: boolean;
}

/**
 * HTTP Service for cross-platform requests
 */
export class HttpService {
  private static isNative = Capacitor.getPlatform() !== 'web';

  /**
   * Make a GET request
   */
  static async get(url: string, options: HttpRequestOptions = {}): Promise<HttpResponseData> {
    if (this.isNative) {
      return this.nativeRequest('GET', url, undefined, options);
    } else {
      return this.webRequest('GET', url, undefined, options);
    }
  }

  /**
   * Make a POST request
   */
  static async post(url: string, data?: any, options: HttpRequestOptions = {}): Promise<HttpResponseData> {
    if (this.isNative) {
      return this.nativeRequest('POST', url, data, options);
    } else {
      return this.webRequest('POST', url, data, options);
    }
  }

  /**
   * Make a PUT request
   */
  static async put(url: string, data?: any, options: HttpRequestOptions = {}): Promise<HttpResponseData> {
    if (this.isNative) {
      return this.nativeRequest('PUT', url, data, options);
    } else {
      return this.webRequest('PUT', url, data, options);
    }
  }

  /**
   * Make a DELETE request
   */
  static async delete(url: string, options: HttpRequestOptions = {}): Promise<HttpResponseData> {
    if (this.isNative) {
      return this.nativeRequest('DELETE', url, undefined, options);
    } else {
      return this.webRequest('DELETE', url, undefined, options);
    }
  }

  /**
   * Native HTTP request using Capacitor
   */
  private static async nativeRequest(
    method: string,
    url: string,
    data?: any,
    options: HttpRequestOptions = {}
  ): Promise<HttpResponseData> {
    try {
      const httpOptions: HttpOptions = {
        url,
        method: method as any,
        headers: options.headers || {},
        data: data,
        connectTimeout: options.timeout || 30000,
        readTimeout: options.timeout || 30000
      };

      const response: HttpResponse = await CapacitorHttp.request(httpOptions);

      return {
        data: response.data,
        status: response.status,
        headers: response.headers,
        ok: response.status >= 200 && response.status < 300
      };
    } catch (error) {
      console.error('Native HTTP request failed:', error);
      throw error;
    }
  }

  /**
   * Web HTTP request using fetch
   */
  private static async webRequest(
    method: string,
    url: string,
    data?: any,
    options: HttpRequestOptions = {}
  ): Promise<HttpResponseData> {
    try {
      const fetchOptions: RequestInit = {
        method,
        headers: options.headers || {},
        signal: options.timeout ? AbortSignal.timeout(options.timeout) : undefined
      };

      if (data) {
        if (typeof data === 'string') {
          fetchOptions.body = data;
        } else {
          fetchOptions.body = JSON.stringify(data);
          fetchOptions.headers = {
            'Content-Type': 'application/json',
            ...fetchOptions.headers
          };
        }
      }

      const response = await fetch(url, fetchOptions);
      
      let responseData: any;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      // Convert Headers to plain object
      const headers: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        headers[key] = value;
      });

      return {
        data: responseData,
        status: response.status,
        headers,
        ok: response.ok
      };
    } catch (error) {
      console.error('Web HTTP request failed:', error);
      throw error;
    }
  }
}

// Legacy exports for backward compatibility
export const NativeHttp = HttpService;
