/**
 * Storage Utilities
 * Unified storage interface for web and native platforms
 */

import { Preferences } from '@capacitor/preferences';
import { Capacitor } from '@capacitor/core';

export interface StorageOptions {
  encrypt?: boolean;
  prefix?: string;
}

/**
 * Cross-platform storage service
 */
export class StorageService {
  private static isNative = Capacitor.getPlatform() !== 'web';
  private static defaultPrefix = 'app_';

  /**
   * Store a value
   */
  static async set(key: string, value: any, options: StorageOptions = {}): Promise<void> {
    try {
      const prefixedKey = this.getPrefixedKey(key, options.prefix);
      const serializedValue = JSON.stringify(value);

      if (this.isNative) {
        await Preferences.set({
          key: prefixedKey,
          value: serializedValue
        });
      } else {
        localStorage.setItem(prefixedKey, serializedValue);
      }
    } catch (error) {
      console.error('Storage set failed:', error);
      throw new Error(`Failed to store value for key: ${key}`);
    }
  }

  /**
   * Get a value
   */
  static async get<T = any>(key: string, options: StorageOptions = {}): Promise<T | null> {
    try {
      const prefixedKey = this.getPrefixedKey(key, options.prefix);
      let value: string | null = null;

      if (this.isNative) {
        const result = await Preferences.get({ key: prefixedKey });
        value = result.value;
      } else {
        value = localStorage.getItem(prefixedKey);
      }

      if (value === null) {
        return null;
      }

      return JSON.parse(value) as T;
    } catch (error) {
      console.error('Storage get failed:', error);
      return null;
    }
  }

  /**
   * Remove a value
   */
  static async remove(key: string, options: StorageOptions = {}): Promise<void> {
    try {
      const prefixedKey = this.getPrefixedKey(key, options.prefix);

      if (this.isNative) {
        await Preferences.remove({ key: prefixedKey });
      } else {
        localStorage.removeItem(prefixedKey);
      }
    } catch (error) {
      console.error('Storage remove failed:', error);
      throw new Error(`Failed to remove value for key: ${key}`);
    }
  }

  /**
   * Clear all values with optional prefix filter
   */
  static async clear(prefix?: string): Promise<void> {
    try {
      const targetPrefix = prefix || this.defaultPrefix;

      if (this.isNative) {
        const { keys } = await Preferences.keys();
        const keysToRemove = keys.filter(key => key.startsWith(targetPrefix));
        
        await Promise.all(
          keysToRemove.map(key => Preferences.remove({ key }))
        );
      } else {
        const keysToRemove: string[] = [];
        
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith(targetPrefix)) {
            keysToRemove.push(key);
          }
        }
        
        keysToRemove.forEach(key => localStorage.removeItem(key));
      }
    } catch (error) {
      console.error('Storage clear failed:', error);
      throw new Error('Failed to clear storage');
    }
  }

  /**
   * Get all keys with optional prefix filter
   */
  static async keys(prefix?: string): Promise<string[]> {
    try {
      const targetPrefix = prefix || this.defaultPrefix;

      if (this.isNative) {
        const { keys } = await Preferences.keys();
        return keys
          .filter(key => key.startsWith(targetPrefix))
          .map(key => key.replace(targetPrefix, ''));
      } else {
        const keys: string[] = [];
        
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith(targetPrefix)) {
            keys.push(key.replace(targetPrefix, ''));
          }
        }
        
        return keys;
      }
    } catch (error) {
      console.error('Storage keys failed:', error);
      return [];
    }
  }

  /**
   * Check if a key exists
   */
  static async has(key: string, options: StorageOptions = {}): Promise<boolean> {
    const value = await this.get(key, options);
    return value !== null;
  }

  /**
   * Get storage info
   */
  static getStorageInfo(): { platform: string; native: boolean } {
    return {
      platform: Capacitor.getPlatform(),
      native: this.isNative
    };
  }

  /**
   * Get prefixed key
   */
  private static getPrefixedKey(key: string, prefix?: string): string {
    const actualPrefix = prefix || this.defaultPrefix;
    return `${actualPrefix}${key}`;
  }
}

/**
 * Specialized storage for authentication data
 */
export class AuthStorage {
  private static readonly AUTH_PREFIX = 'auth_';

  static async setTokens(tokens: any): Promise<void> {
    return StorageService.set('tokens', tokens, { prefix: this.AUTH_PREFIX });
  }

  static async getTokens(): Promise<any> {
    return StorageService.get('tokens', { prefix: this.AUTH_PREFIX });
  }

  static async setUser(user: any): Promise<void> {
    return StorageService.set('user', user, { prefix: this.AUTH_PREFIX });
  }

  static async getUser(): Promise<any> {
    return StorageService.get('user', { prefix: this.AUTH_PREFIX });
  }

  static async clearAuth(): Promise<void> {
    return StorageService.clear(this.AUTH_PREFIX);
  }
}

/**
 * Specialized storage for user preferences
 */
export class PreferencesStorage {
  private static readonly PREFS_PREFIX = 'prefs_';

  static async setTheme(theme: string): Promise<void> {
    return StorageService.set('theme', theme, { prefix: this.PREFS_PREFIX });
  }

  static async getTheme(): Promise<string | null> {
    return StorageService.get('theme', { prefix: this.PREFS_PREFIX });
  }

  static async setLanguage(language: string): Promise<void> {
    return StorageService.set('language', language, { prefix: this.PREFS_PREFIX });
  }

  static async getLanguage(): Promise<string | null> {
    return StorageService.get('language', { prefix: this.PREFS_PREFIX });
  }

  static async clearPreferences(): Promise<void> {
    return StorageService.clear(this.PREFS_PREFIX);
  }
}
