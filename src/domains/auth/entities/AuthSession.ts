/**
 * AuthSession Domain Entity
 * Representa una sesión de autenticación activa
 */

import { User } from './User';
import { OIDCTokens } from './OIDCTokens';

export interface AuthSessionData {
  sessionId: string;
  user: User;
  tokens: OIDCTokens;
  deviceInfo?: DeviceInfo;
  loginMethod: 'oidc' | 'social' | 'manual';
  isActive: boolean;
  createdAt?: Date;
  lastActivityAt?: Date;
}

export interface DeviceInfo {
  platform: string;
  deviceId?: string;
  userAgent?: string;
  appVersion?: string;
}

export class AuthSession {
  public readonly sessionId: string;
  public readonly user: User;
  public readonly tokens: OIDCTokens;
  public readonly deviceInfo?: DeviceInfo;
  public readonly loginMethod: 'oidc' | 'social' | 'manual';
  public readonly isActive: boolean;
  public readonly createdAt: Date;
  public readonly lastActivityAt: Date;

  constructor(data: AuthSessionData) {
    this.sessionId = data.sessionId;
    this.user = data.user;
    this.tokens = data.tokens;
    this.deviceInfo = data.deviceInfo;
    this.loginMethod = data.loginMethod;
    this.isActive = data.isActive;
    this.createdAt = data.createdAt || new Date();
    this.lastActivityAt = data.lastActivityAt || new Date();

    this.validateSession();
  }

  private validateSession(): void {
    if (!this.sessionId || this.sessionId.trim() === '') {
      throw new Error('Session ID is required');
    }

    if (!this.user) {
      throw new Error('User is required for auth session');
    }

    if (!this.tokens) {
      throw new Error('Tokens are required for auth session');
    }
  }

  /**
   * Verifica si la sesión es válida
   */
  isValid(): boolean {
    return (
      this.isActive &&
      !this.tokens.isExpired() &&
      this.user.isActive()
    );
  }

  /**
   * Verifica si la sesión expirará pronto
   */
  isExpiringSoon(): boolean {
    return this.tokens.isExpiringSoon();
  }

  /**
   * Verifica si la sesión puede ser renovada
   */
  canBeRefreshed(): boolean {
    return (
      this.isActive &&
      this.tokens.hasRefreshToken() &&
      this.user.isActive()
    );
  }

  /**
   * Obtiene el tiempo transcurrido desde la última actividad
   */
  getTimeSinceLastActivity(): number {
    return Date.now() - this.lastActivityAt.getTime();
  }

  /**
   * Verifica si la sesión ha estado inactiva por mucho tiempo
   */
  isStale(maxInactiveTime: number = 24 * 60 * 60 * 1000): boolean { // 24 horas por defecto
    return this.getTimeSinceLastActivity() > maxInactiveTime;
  }

  /**
   * Actualiza la actividad de la sesión
   */
  updateActivity(): AuthSession {
    return new AuthSession({
      sessionId: this.sessionId,
      user: this.user,
      tokens: this.tokens,
      deviceInfo: this.deviceInfo,
      loginMethod: this.loginMethod,
      isActive: this.isActive,
      createdAt: this.createdAt,
      lastActivityAt: new Date()
    });
  }

  /**
   * Actualiza los tokens de la sesión (para refresh)
   */
  updateTokens(newTokens: OIDCTokens): AuthSession {
    return new AuthSession({
      sessionId: this.sessionId,
      user: this.user.updateLastLogin(), // Actualiza último login
      tokens: newTokens,
      deviceInfo: this.deviceInfo,
      loginMethod: this.loginMethod,
      isActive: this.isActive,
      createdAt: this.createdAt,
      lastActivityAt: new Date()
    });
  }

  /**
   * Actualiza el usuario de la sesión
   */
  updateUser(updatedUser: User): AuthSession {
    return new AuthSession({
      sessionId: this.sessionId,
      user: updatedUser,
      tokens: this.tokens,
      deviceInfo: this.deviceInfo,
      loginMethod: this.loginMethod,
      isActive: this.isActive,
      createdAt: this.createdAt,
      lastActivityAt: new Date()
    });
  }

  /**
   * Marca la sesión como inactiva (logout)
   */
  deactivate(): AuthSession {
    return new AuthSession({
      sessionId: this.sessionId,
      user: this.user,
      tokens: this.tokens,
      deviceInfo: this.deviceInfo,
      loginMethod: this.loginMethod,
      isActive: false,
      createdAt: this.createdAt,
      lastActivityAt: this.lastActivityAt
    });
  }

  /**
   * Obtiene el token de acceso válido
   */
  getAccessToken(): string | null {
    return this.isValid() ? this.tokens.accessToken : null;
  }

  /**
   * Obtiene el header de autorización
   */
  getAuthorizationHeader(): string | null {
    return this.isValid() ? this.tokens.getAuthorizationHeader() : null;
  }

  /**
   * Convierte la sesión a objeto serializable
   */
  toJSON(): Record<string, any> {
    return {
      sessionId: this.sessionId,
      user: this.user.toJSON(),
      tokens: this.tokens.toJSON(),
      deviceInfo: this.deviceInfo,
      loginMethod: this.loginMethod,
      isActive: this.isActive,
      createdAt: this.createdAt.toISOString(),
      lastActivityAt: this.lastActivityAt.toISOString()
    };
  }

  /**
   * Crea una sesión desde objeto serializado
   */
  static fromJSON(data: any): AuthSession {
    return new AuthSession({
      sessionId: data.sessionId,
      user: User.fromJSON(data.user),
      tokens: OIDCTokens.fromJSON(data.tokens),
      deviceInfo: data.deviceInfo,
      loginMethod: data.loginMethod,
      isActive: data.isActive,
      createdAt: data.createdAt ? new Date(data.createdAt) : undefined,
      lastActivityAt: data.lastActivityAt ? new Date(data.lastActivityAt) : undefined
    });
  }

  /**
   * Crea una nueva sesión de autenticación
   */
  static create(
    user: User,
    tokens: OIDCTokens,
    deviceInfo?: DeviceInfo,
    loginMethod: 'oidc' | 'social' | 'manual' = 'oidc'
  ): AuthSession {
    const sessionId = this.generateSessionId();
    
    return new AuthSession({
      sessionId,
      user,
      tokens,
      deviceInfo,
      loginMethod,
      isActive: true
    });
  }

  /**
   * Genera un ID de sesión único
   */
  private static generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2);
    return `session_${timestamp}_${randomStr}`;
  }
}
