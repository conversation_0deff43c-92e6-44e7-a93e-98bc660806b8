/**
 * User Domain Entity
 * Representa un usuario en el dominio de la aplicación
 */

export interface Student {
  id: string;
  name: string;
  grade: string;
  school: string;
  class?: string;
  avatar?: string;
  isActive: boolean;
}

export interface UserProfile {
  id: string;
  sub: string; // OIDC subject identifier
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  locale: string;
  timezone: string;
  memberSince: string;
  roles: string[];
  isActive: boolean;
}

export class User {
  public readonly id: string;
  public readonly profile: UserProfile;
  public readonly children: Student[];
  public readonly permissions: string[];
  public readonly lastLoginAt: Date;
  public readonly createdAt: Date;
  public readonly updatedAt: Date;

  constructor(
    id: string,
    profile: UserProfile,
    children: Student[] = [],
    permissions: string[] = [],
    lastLoginAt?: Date,
    createdAt?: Date,
    updatedAt?: Date
  ) {
    this.id = id;
    this.profile = profile;
    this.children = children;
    this.permissions = permissions;
    this.lastLoginAt = lastLoginAt || new Date();
    this.createdAt = createdAt || new Date();
    this.updatedAt = updatedAt || new Date();

    // Validate required fields
    this.validateUser();
  }

  private validateUser(): void {
    if (!this.id || this.id.trim() === '') {
      throw new Error('User ID is required');
    }

    if (!this.profile.email || !this.isValidEmail(this.profile.email)) {
      throw new Error('Valid email is required');
    }

    if (!this.profile.name || this.profile.name.trim() === '') {
      throw new Error('User name is required');
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Business logic methods

  /**
   * Verifica si el usuario tiene un permiso específico
   */
  hasPermission(permission: string): boolean {
    return this.permissions.includes(permission);
  }

  /**
   * Verifica si el usuario tiene alguno de los roles especificados
   */
  hasRole(role: string): boolean {
    return this.profile.roles.includes(role);
  }

  /**
   * Verifica si el usuario es padre/madre
   */
  isParent(): boolean {
    return this.hasRole('parent') || this.hasRole('familiar');
  }

  /**
   * Verifica si el usuario tiene hijos activos
   */
  hasActiveChildren(): boolean {
    return this.children.some(child => child.isActive);
  }

  /**
   * Obtiene el primer hijo activo (para selección por defecto)
   */
  getDefaultChild(): Student | null {
    return this.children.find(child => child.isActive) || null;
  }

  /**
   * Obtiene un hijo por ID
   */
  getChildById(childId: string): Student | undefined {
    return this.children.find(child => child.id === childId);
  }

  /**
   * Obtiene el nombre completo formateado
   */
  getDisplayName(): string {
    return this.profile.name;
  }

  /**
   * Obtiene la URL del avatar o un avatar por defecto
   */
  getAvatarUrl(): string {
    return this.profile.avatar || '/assets/icons/default-avatar.png';
  }

  /**
   * Verifica si la cuenta del usuario está activa
   */
  isActive(): boolean {
    return this.profile.isActive;
  }

  /**
   * Crea una copia del usuario con datos actualizados
   */
  updateProfile(updates: Partial<UserProfile>): User {
    const updatedProfile = { ...this.profile, ...updates };
    return new User(
      this.id,
      updatedProfile,
      this.children,
      this.permissions,
      this.lastLoginAt,
      this.createdAt,
      new Date() // updatedAt
    );
  }

  /**
   * Crea una copia del usuario con hijos actualizados
   */
  updateChildren(children: Student[]): User {
    return new User(
      this.id,
      this.profile,
      children,
      this.permissions,
      this.lastLoginAt,
      this.createdAt,
      new Date() // updatedAt
    );
  }

  /**
   * Crea una copia del usuario con último login actualizado
   */
  updateLastLogin(): User {
    return new User(
      this.id,
      this.profile,
      this.children,
      this.permissions,
      new Date(), // lastLoginAt
      this.createdAt,
      new Date() // updatedAt
    );
  }

  /**
   * Convierte el usuario a un objeto serializable
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      profile: this.profile,
      children: this.children,
      permissions: this.permissions,
      lastLoginAt: this.lastLoginAt.toISOString(),
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString()
    };
  }

  /**
   * Crea un usuario desde un objeto serializado
   */
  static fromJSON(data: any): User {
    return new User(
      data.id,
      data.profile,
      data.children || [],
      data.permissions || [],
      data.lastLoginAt ? new Date(data.lastLoginAt) : undefined,
      data.createdAt ? new Date(data.createdAt) : undefined,
      data.updatedAt ? new Date(data.updatedAt) : undefined
    );
  }
}
