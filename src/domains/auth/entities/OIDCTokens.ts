/**
 * OIDCTokens Value Object
 * Representa los tokens de OpenID Connect de forma inmutable
 */

export interface OIDCTokenData {
  accessToken: string;
  idToken: string;
  refreshToken?: string;
  tokenType: string;
  expiresIn: number;
  scope: string;
  issuedAt: number;
}

export class OIDCTokens {
  public readonly accessToken: string;
  public readonly idToken: string;
  public readonly refreshToken?: string;
  public readonly tokenType: string;
  public readonly expiresIn: number;
  public readonly scope: string;
  public readonly issuedAt: number;
  public readonly expiresAt: number;

  constructor(data: OIDCTokenData) {
    // Validaciones
    if (!data.accessToken || data.accessToken.trim() === '') {
      throw new Error('Access token is required');
    }

    if (!data.idToken || data.idToken.trim() === '') {
      throw new Error('ID token is required');
    }

    if (data.expiresIn <= 0) {
      throw new Error('ExpiresIn must be positive');
    }

    this.accessToken = data.accessToken;
    this.idToken = data.idToken;
    this.refreshToken = data.refreshToken;
    this.tokenType = data.tokenType || 'Bearer';
    this.expiresIn = data.expiresIn;
    this.scope = data.scope;
    this.issuedAt = data.issuedAt || Date.now();
    this.expiresAt = this.issuedAt + (this.expiresIn * 1000);
  }

  /**
   * Verifica si los tokens han expirado
   */
  isExpired(): boolean {
    return Date.now() >= this.expiresAt;
  }

  /**
   * Verifica si los tokens expirarán pronto (en los próximos 5 minutos)
   */
  isExpiringSoon(): boolean {
    const fiveMinutesInMs = 5 * 60 * 1000;
    return Date.now() >= (this.expiresAt - fiveMinutesInMs);
  }

  /**
   * Obtiene el tiempo restante hasta la expiración en segundos
   */
  getTimeToExpiry(): number {
    const timeLeft = Math.max(0, this.expiresAt - Date.now());
    return Math.floor(timeLeft / 1000);
  }

  /**
   * Verifica si tiene refresh token disponible
   */
  hasRefreshToken(): boolean {
    return !!this.refreshToken && this.refreshToken.trim() !== '';
  }

  /**
   * Obtiene el header de autorización HTTP
   */
  getAuthorizationHeader(): string {
    return `${this.tokenType} ${this.accessToken}`;
  }

  /**
   * Parsea los claims del ID token (sin verificar la firma)
   */
  getIdTokenClaims(): any {
    try {
      const parts = this.idToken.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      const payload = parts[1];
      const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
      return JSON.parse(decoded);
    } catch (error) {
      throw new Error('Failed to parse ID token claims');
    }
  }

  /**
   * Obtiene el subject (sub) del ID token
   */
  getUserId(): string {
    const claims = this.getIdTokenClaims();
    return claims.sub;
  }

  /**
   * Obtiene el email del ID token
   */
  getUserEmail(): string {
    const claims = this.getIdTokenClaims();
    return claims.email;
  }

  /**
   * Crea nuevos tokens con access token actualizado (para refresh)
   */
  withNewAccessToken(newAccessToken: string, newExpiresIn: number): OIDCTokens {
    return new OIDCTokens({
      accessToken: newAccessToken,
      idToken: this.idToken, // ID token no cambia en refresh
      refreshToken: this.refreshToken,
      tokenType: this.tokenType,
      expiresIn: newExpiresIn,
      scope: this.scope,
      issuedAt: Date.now()
    });
  }

  /**
   * Crea nuevos tokens completos (para nuevo login)
   */
  static create(
    accessToken: string,
    idToken: string,
    refreshToken: string | undefined,
    expiresIn: number,
    scope: string,
    tokenType: string = 'Bearer'
  ): OIDCTokens {
    return new OIDCTokens({
      accessToken,
      idToken,
      refreshToken,
      tokenType,
      expiresIn,
      scope,
      issuedAt: Date.now()
    });
  }

  /**
   * Convierte a objeto serializable para almacenamiento
   */
  toJSON(): Record<string, any> {
    return {
      accessToken: this.accessToken,
      idToken: this.idToken,
      refreshToken: this.refreshToken,
      tokenType: this.tokenType,
      expiresIn: this.expiresIn,
      scope: this.scope,
      issuedAt: this.issuedAt,
      expiresAt: this.expiresAt
    };
  }

  /**
   * Crea tokens desde objeto serializado
   */
  static fromJSON(data: any): OIDCTokens {
    return new OIDCTokens({
      accessToken: data.accessToken,
      idToken: data.idToken,
      refreshToken: data.refreshToken,
      tokenType: data.tokenType || 'Bearer',
      expiresIn: data.expiresIn,
      scope: data.scope,
      issuedAt: data.issuedAt
    });
  }

  /**
   * Crea tokens desde respuesta de API OIDC
   */
  static fromApiResponse(response: {
    access_token: string;
    id_token: string;
    refresh_token?: string;
    token_type?: string;
    expires_in: number;
    scope: string;
  }): OIDCTokens {
    return new OIDCTokens({
      accessToken: response.access_token,
      idToken: response.id_token,
      refreshToken: response.refresh_token,
      tokenType: response.token_type || 'Bearer',
      expiresIn: response.expires_in,
      scope: response.scope,
      issuedAt: Date.now()
    });
  }
}
