/**
 * AuthService - Domain Service
 * Contiene la lógica de negocio para autenticación
 */

import { AuthRepository, OIDCAuthResult, OIDCUserInfo } from '../repositories/AuthRepository';
import { UserRepository } from '../repositories/UserRepository';
import { AuthSession } from '../entities/AuthSession';
import { User, UserProfile } from '../entities/User';
import { OIDCTokens } from '../entities/OIDCTokens';

export interface LoginResult {
  session: AuthSession;
  isNewUser: boolean;
}

export interface AuthState {
  isAuthenticated: boolean;
  session: AuthSession | null;
  isLoading: boolean;
  error: string | null;
}

export class AuthService {
  private authRepository: AuthRepository;
  private userRepository: UserRepository;

  constructor(authRepository: AuthRepository, userRepository: UserRepository) {
    this.authRepository = authRepository;
    this.userRepository = userRepository;
  }

  /**
   * Inicia el proceso de login con OIDC
   */
  async login(): Promise<string> {
    try {
      // Limpiar cualquier sesión existente
      await this.authRepository.clearSession();
      await this.authRepository.clearAuthRequest();

      // Iniciar flujo OIDC
      const authUrl = await this.authRepository.initiateOIDCAuth();
      
      return authUrl;
    } catch (error) {
      throw new Error(`Failed to initiate login: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Procesa el callback de OIDC y completa el login
   */
  async processCallback(code: string, state: string): Promise<LoginResult> {
    try {
      // Validar estado CSRF
      const isValidState = await this.authRepository.validateState(state);
      if (!isValidState) {
        throw new Error('Invalid state parameter - possible CSRF attack');
      }

      // Obtener petición de auth almacenada
      const authRequest = await this.authRepository.getAuthRequest();
      if (!authRequest) {
        throw new Error('No auth request found');
      }

      // Intercambiar código por tokens
      const authResult: OIDCAuthResult = { code, state };
      const tokens = await this.authRepository.processOIDCCallback(authResult, authRequest);

      // Obtener información del usuario
      const oidcUserInfo = await this.authRepository.getUserInfo(tokens.accessToken);

      // Crear o recuperar usuario del dominio
      const user = await this.createOrUpdateUser(oidcUserInfo, tokens);

      // Crear sesión de autenticación
      const session = AuthSession.create(user, tokens, undefined, 'oidc');

      // Almacenar sesión
      await this.authRepository.storeSession(session);

      // Limpiar petición de auth
      await this.authRepository.clearAuthRequest();

      // Determinar si es usuario nuevo
      const isNewUser = await this.isNewUser(user.id);

      return { session, isNewUser };
    } catch (error) {
      // Limpiar estado en caso de error
      await this.authRepository.clearAuthRequest();
      await this.authRepository.clearSession();
      
      throw new Error(`Login failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Obtiene la sesión actual si existe y es válida
   */
  async getCurrentSession(): Promise<AuthSession | null> {
    try {
      const session = await this.authRepository.getCurrentSession();
      
      if (!session) {
        return null;
      }

      // Verificar si la sesión es válida
      if (!session.isValid()) {
        // Intentar renovar si es posible
        if (session.canBeRefreshed()) {
          try {
            const refreshedSession = await this.refreshSession(session);
            return refreshedSession;
          } catch (error) {
            // Si no se puede renovar, limpiar sesión
            await this.authRepository.clearSession();
            return null;
          }
        } else {
          // Limpiar sesión inválida
          await this.authRepository.clearSession();
          return null;
        }
      }

      // Actualizar actividad de la sesión
      const updatedSession = session.updateActivity();
      await this.authRepository.storeSession(updatedSession);

      return updatedSession;
    } catch (error) {
      console.error('Error getting current session:', error);
      return null;
    }
  }

  /**
   * Renueva una sesión usando refresh token
   */
  async refreshSession(session: AuthSession): Promise<AuthSession> {
    if (!session.canBeRefreshed()) {
      throw new Error('Session cannot be refreshed');
    }

    try {
      // Renovar tokens
      const newTokens = await this.authRepository.refreshTokens(session.tokens.refreshToken!);
      
      // Actualizar sesión con nuevos tokens
      const refreshedSession = session.updateTokens(newTokens);
      
      // Almacenar sesión actualizada
      await this.authRepository.storeSession(refreshedSession);

      return refreshedSession;
    } catch (error) {
      throw new Error(`Failed to refresh session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Cierra la sesión del usuario
   */
  async logout(): Promise<void> {
    try {
      const session = await this.authRepository.getCurrentSession();
      
      if (session) {
        // Revocar tokens en el proveedor
        try {
          await this.authRepository.revokeTokens(session.tokens);
        } catch (error) {
          console.warn('Failed to revoke tokens:', error);
          // Continuar con logout local incluso si falla la revocación remota
        }
      }

      // Limpiar sesión local
      await this.authRepository.clearSession();
      await this.authRepository.clearAuthRequest();
    } catch (error) {
      throw new Error(`Logout failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Obtiene un token de acceso válido
   */
  async getAccessToken(): Promise<string | null> {
    const session = await this.getCurrentSession();
    return session?.getAccessToken() || null;
  }

  /**
   * Verifica si el usuario está autenticado
   */
  async isAuthenticated(): Promise<boolean> {
    const session = await this.getCurrentSession();
    return session?.isValid() ?? false;
  }

  /**
   * Actualiza los datos del usuario
   */
  async updateUser(updates: Partial<UserProfile>): Promise<User> {
    const session = await this.getCurrentSession();
    if (!session) {
      throw new Error('No active session');
    }

    try {
      // Actualizar en repositorio
      const updatedUser = await this.userRepository.updateUser(session.user.id, updates);
      
      // Actualizar sesión
      const updatedSession = session.updateUser(updatedUser);
      await this.authRepository.storeSession(updatedSession);

      return updatedUser;
    } catch (error) {
      throw new Error(`Failed to update user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Sincroniza los datos del usuario con sistemas externos
   */
  async syncUserData(): Promise<User> {
    const session = await this.getCurrentSession();
    if (!session) {
      throw new Error('No active session');
    }

    try {
      const syncedUser = await this.userRepository.syncUserData(session.user.id);
      
      // Actualizar sesión con datos sincronizados
      const updatedSession = session.updateUser(syncedUser);
      await this.authRepository.storeSession(updatedSession);

      return syncedUser;
    } catch (error) {
      throw new Error(`Failed to sync user data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Crea o actualiza un usuario basado en información OIDC
   */
  private async createOrUpdateUser(oidcUserInfo: OIDCUserInfo, tokens: OIDCTokens): Promise<User> {
    try {
      // Primero intentar obtener usuario existente
      let user = await this.userRepository.getUserById(oidcUserInfo.sub);
      
      if (user) {
        // Usuario existe, actualizar con información OIDC
        const updatedProfile: Partial<UserProfile> = {
          name: oidcUserInfo.name || user.profile.name,
          email: oidcUserInfo.email || user.profile.email,
          avatar: oidcUserInfo.picture || user.profile.avatar,
          locale: oidcUserInfo.locale || user.profile.locale,
          timezone: oidcUserInfo.timezone || user.profile.timezone,
        };
        
        user = user.updateProfile(updatedProfile);
        return await this.userRepository.updateUser(user.id, updatedProfile);
      } else {
        // Usuario nuevo, crear desde información OIDC
        const userProfile: UserProfile = {
          id: oidcUserInfo.sub,
          sub: oidcUserInfo.sub,
          name: oidcUserInfo.name || oidcUserInfo.preferred_username || 'Usuario',
          email: oidcUserInfo.email,
          avatar: oidcUserInfo.picture,
          locale: oidcUserInfo.locale || 'es-ES',
          timezone: oidcUserInfo.timezone || 'Europe/Madrid',
          memberSince: new Date().toISOString(),
          roles: ['parent'], // Rol por defecto
          isActive: true
        };

        // Obtener hijos del usuario si existen
        const children = await this.userRepository.getUserChildren(oidcUserInfo.sub);
        
        // Crear usuario
        const newUser = new User(
          oidcUserInfo.sub,
          userProfile,
          children,
          [], // Permisos se cargarán después
          new Date()
        );

        return await this.userRepository.createUser(newUser);
      }
    } catch (error) {
      throw new Error(`Failed to create or update user: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Verifica si es un usuario nuevo (primera vez que se loguea)
   */
  private async isNewUser(userId: string): Promise<boolean> {
    try {
      const existedBefore = await this.userRepository.userExists(userId);
      return !existedBefore;
    } catch (error) {
      console.warn('Could not determine if user is new:', error);
      return false; // Por defecto asumir que no es nuevo
    }
  }
}
