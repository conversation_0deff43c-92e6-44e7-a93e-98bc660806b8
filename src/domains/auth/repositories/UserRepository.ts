/**
 * UserRepository Interface
 * Define el contrato para el acceso a datos de usuario (SCIM API)
 */

import { User, Student } from '../entities/User';

export interface SCIMUser {
  id: string;
  schemas: string[];
  meta: {
    resourceType: string;
    created: string;
    lastModified: string;
    version: string;
    location: string;
  };
  userName: string;
  externalId: string;
  name: {
    givenName: string;
    familyName: string;
    middleName?: string;
    formatted: string;
  };
  emails: Array<{
    value: string;
    primary: boolean;
  }>;
  active: boolean;
  phoneNumbers?: Array<{
    value: string;
  }>;
  userType: string;
  locale: string;
  timezone: string;
  origin: string;
}

export interface SCIMSearchResponse {
  schemas: string[];
  totalResults: number;
  startIndex: number;
  itemsPerPage: number;
  resources: SCIMUser[];
}

/**
 * Repositorio para operaciones de datos de usuario
 */
export interface UserRepository {
  /**
   * Obtiene un usuario por su ID
   */
  getUserById(userId: string): Promise<User | null>;

  /**
   * Obtiene un usuario por su email
   */
  getUserByEmail(email: string): Promise<User | null>;

  /**
   * Busca usuarios usando filtros SCIM
   */
  searchUsers(filter: string): Promise<User[]>;

  /**
   * Obtiene el perfil extendido de un usuario desde SCIM
   */
  getUserProfile(userId: string): Promise<SCIMUser | null>;

  /**
   * Obtiene los hijos asociados a un usuario familiar
   */
  getUserChildren(userId: string): Promise<Student[]>;

  /**
   * Actualiza los datos básicos del usuario
   */
  updateUser(userId: string, updates: Partial<User>): Promise<User>;

  /**
   * Verifica si un usuario existe en el sistema
   */
  userExists(userId: string): Promise<boolean>;

  /**
   * Obtiene los permisos de un usuario
   */
  getUserPermissions(userId: string): Promise<string[]>;

  /**
   * Obtiene los roles de un usuario
   */
  getUserRoles(userId: string): Promise<string[]>;

  /**
   * Crea un nuevo usuario (si es necesario)
   */
  createUser(userData: Partial<User>): Promise<User>;

  /**
   * Sincroniza los datos del usuario con sistemas externos
   */
  syncUserData(userId: string): Promise<User>;
}
