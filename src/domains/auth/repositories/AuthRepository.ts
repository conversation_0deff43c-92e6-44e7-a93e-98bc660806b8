/**
 * AuthRepository Interface
 * Define el contrato para la persistencia de datos de autenticación
 */

import { AuthSession } from '../entities/AuthSession';
import { OIDCTokens } from '../entities/OIDCTokens';

export interface OIDCAuthRequest {
  state: string;
  nonce: string;
  codeVerifier: string;
  redirectUri: string;
  scope: string;
}

export interface OIDCAuthResult {
  code: string;
  state: string;
}

export interface OIDCTokenResponse {
  access_token: string;
  id_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

export interface OIDCUserInfo {
  sub: string;
  name: string;
  email: string;
  email_verified: boolean;
  preferred_username?: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
  locale?: string;
  timezone?: string;
  [key: string]: any;
}

/**
 * Repositorio para operaciones de autenticación OIDC
 */
export interface AuthRepository {
  /**
   * Inicia el flujo de autenticación OIDC
   */
  initiateOIDCAuth(): Promise<string>; // Returns authorization URL

  /**
   * Procesa el callback de OIDC y obtiene los tokens
   */
  processOIDCCallback(authResult: OIDCAuthResult, authRequest: OIDCAuthRequest): Promise<OIDCTokens>;

  /**
   * Obtiene información del usuario desde el proveedor OIDC
   */
  getUserInfo(accessToken: string): Promise<OIDCUserInfo>;

  /**
   * Renueva los tokens usando refresh token
   */
  refreshTokens(refreshToken: string): Promise<OIDCTokens>;

  /**
   * Revoca los tokens en el proveedor OIDC
   */
  revokeTokens(tokens: OIDCTokens): Promise<void>;

  /**
   * Almacena la sesión de autenticación
   */
  storeSession(session: AuthSession): Promise<void>;

  /**
   * Recupera la sesión actual
   */
  getCurrentSession(): Promise<AuthSession | null>;

  /**
   * Elimina la sesión almacenada
   */
  clearSession(): Promise<void>;

  /**
   * Almacena el estado de la petición OIDC
   */
  storeAuthRequest(request: OIDCAuthRequest): Promise<void>;

  /**
   * Recupera el estado de la petición OIDC
   */
  getAuthRequest(): Promise<OIDCAuthRequest | null>;

  /**
   * Limpia el estado de la petición OIDC
   */
  clearAuthRequest(): Promise<void>;

  /**
   * Valida el estado CSRF de la respuesta OIDC
   */
  validateState(receivedState: string): Promise<boolean>;
}
