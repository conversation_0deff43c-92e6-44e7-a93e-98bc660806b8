import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';
import { CapacitorAuthService } from '../services/capacitor-auth.service';
import { UserApiService } from '../services/user-api.service';
import { debugLog } from '../config/environment.config';

/**
 * Simplified User Context for Capacitor-native authentication
 * Streamlined for optimal mobile performance and user experience
 */

export interface Student {
  id: string;
  name: string;
  grade: string;
  school: string;
  avatar?: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  avatar: string;
  memberSince: string;
  children: Student[];
}

interface UserContextType {
  user: User | null;
  isAuthenticated: boolean;
  selectedStudent: Student | null;
  isLoading: boolean;
  selectStudent: (student: Student) => void;
  updateUser: (userData: Partial<User>) => void;
  logout: () => Promise<void>;
  updateCapacitorAuthState?: (authResult?: any) => Promise<void>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

const SELECTED_STUDENT_KEY = 'selected-student';

interface UserProviderProps {
  children: ReactNode;
}

// Helper function to create User object from auth result using API service
const createUserFromAuthResult = async (authResult: any): Promise<User> => {
  const profile = authResult.profile || {};
  const accessToken = authResult.accessToken;

  debugLog('CapacitorUserContext - Creating user from auth result with API integration', {
    sub: profile.sub,
    name: profile.name,
    email: profile.email,
    hasAccessToken: !!accessToken
  });

  try {
    // Try to get user data from API
    const apiUserData = await UserApiService.fetchUserProfile(authResult.profile, accessToken);
    
    if (apiUserData) {
      debugLog('CapacitorUserContext - Successfully retrieved user data from API');
      return apiUserData;
    }
  } catch (error) {
    debugLog('CapacitorUserContext - API call failed, using profile data', error);
  }

  // Fallback to profile data with mock children
  const userData: User = {
    id: profile.sub || 'unknown',
    name: profile.name || profile.preferred_username || 'Usuario',
    email: profile.email || '<EMAIL>',
    phone: profile.phone_number || undefined,
    role: 'parent',
    avatar: profile.picture || '/img/default-avatar.png',
    memberSince: new Date().toISOString().split('T')[0],
    children: [
      {
        id: '1',
        name: 'Estudiante Demo',
        grade: '5° Primaria',
        school: 'Colegio Demo',
        avatar: '/img/student-avatar.png'
      }
    ]
  };

  debugLog('CapacitorUserContext - Created user object from profile', {
    userId: userData.id,
    userName: userData.name,
    userEmail: userData.email,
    childrenCount: userData.children.length
  });

  return userData;
};

// Helper function to load selected student from storage
const loadSelectedStudent = async (userData: User): Promise<Student | null> => {
  try {
    const stored = localStorage.getItem(SELECTED_STUDENT_KEY);
    if (stored) {
      const selectedId = JSON.parse(stored);
      const student = userData.children.find(child => child.id === selectedId);
      if (student) {
        return student;
      }
    }
  } catch (error) {
    debugLog('CapacitorUserContext - Error loading selected student', error);
  }

  // Return first child as default
  if (userData.children.length > 0) {
    return userData.children[0];
  }
  return null;
};

export const CapacitorUserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  // Initialize user context on mount
  useEffect(() => {
    const initializeUserContext = async () => {
      try {
        debugLog('CapacitorUserContext - Initializing Capacitor-native user context');

        // Check for existing Capacitor authentication
        const existingAuth = await CapacitorAuthService.getCurrentUser();
        if (existingAuth) {
          debugLog('CapacitorUserContext - Found existing Capacitor authentication');
          
          // Create user object from existing auth
          const userData = await createUserFromAuthResult(existingAuth);
          setUser(userData);
          setIsAuthenticated(true);

          // Load selected student
          const student = await loadSelectedStudent(userData);
          setSelectedStudent(student);

          debugLog('CapacitorUserContext - Capacitor user initialized successfully', {
            userName: userData.name,
            userEmail: userData.email,
            childrenCount: userData.children.length
          });
        }

        setIsInitialized(true);
        debugLog('CapacitorUserContext - User context initialization completed');

      } catch (error) {
        console.error('❌ CapacitorUserContext - Error initializing user context:', error);
        debugLog('CapacitorUserContext - Error during initialization', error);
        setIsInitialized(true); // Set to true even on error to prevent infinite loading
      }
    };

    if (!isInitialized) {
      initializeUserContext();
    }
  }, [isInitialized]);

  // Select student function
  const selectStudent = useCallback((student: Student) => {
    setSelectedStudent(student);
    localStorage.setItem(SELECTED_STUDENT_KEY, JSON.stringify(student.id));
    debugLog('CapacitorUserContext - Student selected', { studentName: student.name });
  }, []);

  // Update user function
  const updateUser = useCallback((userData: Partial<User>) => {
    setUser(prevUser => {
      if (!prevUser) return null;
      const updatedUser = { ...prevUser, ...userData };
      debugLog('CapacitorUserContext - User updated', { userName: updatedUser.name });
      return updatedUser;
    });
  }, []);

  // Logout function
  const logout = useCallback(async () => {
    try {
      debugLog('CapacitorUserContext - Starting logout');
      
      await CapacitorAuthService.signOut();
      
      setUser(null);
      setSelectedStudent(null);
      setIsAuthenticated(false);
      localStorage.removeItem(SELECTED_STUDENT_KEY);
      
      debugLog('CapacitorUserContext - Logout completed');
    } catch (error) {
      console.error('❌ CapacitorUserContext - Error during logout:', error);
      throw error;
    }
  }, []);

  // Method to update Capacitor authentication state
  const updateCapacitorAuthState = useCallback(async (authResult?: any) => {
    if (authResult) {
      debugLog('CapacitorUserContext - Updating Capacitor auth state with new user');

      // Create user object from auth result
      const userData = await createUserFromAuthResult(authResult);
      setUser(userData);
      setIsAuthenticated(true);

      // Load selected student
      const student = await loadSelectedStudent(userData);
      setSelectedStudent(student);

      debugLog('CapacitorUserContext - Capacitor auth state updated successfully', {
        userName: userData.name,
        userEmail: userData.email,
        isAuthenticated: true
      });
    } else {
      debugLog('CapacitorUserContext - Clearing Capacitor auth state');
      setUser(null);
      setSelectedStudent(null);
      setIsAuthenticated(false);
    }
  }, []);

  const value: UserContextType = {
    user,
    isAuthenticated,
    selectedStudent,
    isLoading: !isInitialized,
    logout,
    selectStudent,
    updateUser,
    updateCapacitorAuthState,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a CapacitorUserProvider');
  }
  return context;
};

// Export alias for consistency
export const useCapacitorUser = useUser;
