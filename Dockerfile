# Multi-stage Dockerfile for Agenda Familiar
# Supports development, pre-production, and production environments

# ----------  BUILD STAGE  ----------
FROM node:24.1.0-bullseye-slim AS build

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --omit=dev

# Copy source code
COPY . .

# Build the application
RUN npm run build

# ----------  DEVELOPMENT STAGE  ----------
FROM node:24.1.0-bullseye-slim AS development

ENV NODE_ENV=development
WORKDIR /app

# Install all dependencies (including dev)
COPY package*.json ./
RUN npm ci

# Copy source code
COPY . .

# Expose development port
EXPOSE 5173

# Start development server
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "5173"]

# ----------  PRE-PRODUCTION STAGE  ----------
FROM node:22.15.1-bullseye-slim AS pre-production

WORKDIR /srv

# Install serve globally
RUN npm install -g serve@latest

# Copy built application from build stage
COPY --from=build /app/dist ./dist

# Expose pre-production port
EXPOSE 4173

# Start pre-production server
CMD ["serve", "-s", "dist", "-l", "4173"]

# ----------  PRODUCTION STAGE  ----------
FROM nginx:1.27-alpine AS production

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy built application from build stage
COPY --from=build /app/dist /usr/share/nginx/html

# Expose production port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
