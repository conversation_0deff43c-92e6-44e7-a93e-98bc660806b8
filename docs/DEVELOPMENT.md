# Development Guide

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- iOS development: Xcode 14+
- Android development: Android Studio with SDK 33+

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd agenda-familiar

# Install dependencies
npm install

# Install Capacitor dependencies
npx cap sync
```

### Development Server

```bash
# Start development server with hot reload
npm run dev

# The app will be available at https://localhost:5173
```

### Mobile Development

#### iOS Development

```bash
# Build and open in Xcode
npx cap build ios
npx cap open ios

# Run directly on device/simulator
npx cap run ios
```

#### Android Development

```bash
# Build and open in Android Studio
npx cap build android
npx cap open android

# Run directly on device/emulator
npx cap run android
```

## Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── common/          # Generic components
│   ├── layout/          # Layout components
│   └── features/        # Feature-specific components
├── pages/               # Page components
├── services/            # Business logic services
├── hooks/               # Custom React hooks
├── contexts/            # React contexts
├── config/              # Configuration files
├── utils/               # Utility functions
├── types/               # TypeScript type definitions
└── styles/              # Global styles
```

## Development Workflow

### 1. Feature Development

1. Create feature branch: `git checkout -b feature/feature-name`
2. Develop in `src/` following the established structure
3. Add tests for new functionality
4. Test on both web and mobile platforms
5. Create pull request

### 2. Component Development

```typescript
// Example component structure
import React from 'react';
import { IonButton } from '@ionic/react';
import './MyComponent.css';

interface MyComponentProps {
  title: string;
  onAction: () => void;
}

const MyComponent: React.FC<MyComponentProps> = ({ title, onAction }) => {
  return (
    <IonButton onClick={onAction}>
      {title}
    </IonButton>
  );
};

export default MyComponent;
```

### 3. Service Development

```typescript
// Example service structure
export class MyService {
  static async getData(): Promise<Data[]> {
    try {
      const response = await fetch('/api/data');
      return await response.json();
    } catch (error) {
      console.error('Error fetching data:', error);
      throw error;
    }
  }
}
```

## Testing

### Unit Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### E2E Tests

```bash
# Run Cypress tests
npm run test:e2e

# Open Cypress UI
npm run test:e2e:open
```

### Mobile Testing

1. **iOS Simulator**: Test on various iOS device sizes
2. **Android Emulator**: Test on different Android versions
3. **Physical Devices**: Test on real devices for performance

## Code Quality

### Linting

```bash
# Run ESLint
npm run lint

# Fix linting issues
npm run lint:fix
```

### TypeScript

```bash
# Type checking
npm run type-check

# Build TypeScript
npm run build
```

### Code Formatting

The project uses Prettier for code formatting. Configure your editor to format on save.

## Environment Configuration

### Development Environment

Create `.env.local` file:

```bash
VITE_OIDC_AUTHORITY=https://pre-identity.santillanaconnect.com
VITE_OIDC_CLIENT_ID=sumun_office_co_pre
VITE_OIDC_SCOPE=openid profile email offline_access neds/full_access
VITE_DEBUG_AUTH=true
```

### Production Environment

Environment variables are configured in the deployment pipeline.

## Debugging

### Web Debugging

1. Use browser developer tools
2. Enable debug mode with `VITE_DEBUG_AUTH=true`
3. Check console for authentication logs
4. Use React Developer Tools extension

### Mobile Debugging

#### iOS Debugging

1. Connect device to Mac
2. Open Safari > Develop > [Device] > [App]
3. Use Web Inspector for debugging

#### Android Debugging

1. Enable USB debugging on device
2. Open Chrome > chrome://inspect
3. Select device and inspect WebView

### Remote Debugging

For production debugging on mobile devices:

```bash
# Enable Chrome DevTools for Android
adb shell am start -n com.santillana.agendafamiliar/com.getcapacitor.MainActivity -e "debug" "true"
```

## Performance Optimization

### Bundle Analysis

```bash
# Analyze bundle size
npm run build
npm run analyze
```

### Mobile Performance

1. **Lazy Loading**: Use React.lazy for route-based code splitting
2. **Image Optimization**: Use appropriate image formats and sizes
3. **Memory Management**: Avoid memory leaks in useEffect hooks
4. **Network Optimization**: Implement proper caching strategies

## Common Issues

### Authentication Issues

1. **CORS Errors**: Use Browser plugin for mobile authentication
2. **Redirect URI Mismatch**: Ensure exact match in OAuth provider
3. **Token Storage**: Use localStorage for token management

### Build Issues

1. **TypeScript Errors**: Run `npm run type-check` to identify issues
2. **Dependency Conflicts**: Clear node_modules and reinstall
3. **Capacitor Sync Issues**: Run `npx cap sync` after dependency changes

### Mobile Platform Issues

1. **iOS Build Failures**: Check Xcode version and iOS deployment target
2. **Android Build Failures**: Verify Android SDK and Gradle versions
3. **Plugin Issues**: Ensure Capacitor plugins are properly installed

## Best Practices

### Code Organization

1. **Single Responsibility**: Each component/service should have one purpose
2. **Consistent Naming**: Use descriptive, consistent naming conventions
3. **Type Safety**: Use TypeScript interfaces for all data structures
4. **Error Handling**: Implement proper error boundaries and handling

### Performance

1. **Memoization**: Use React.memo, useMemo, useCallback appropriately
2. **Lazy Loading**: Implement code splitting for better performance
3. **Caching**: Use appropriate caching strategies for API calls
4. **Bundle Size**: Monitor and optimize bundle size regularly

### Security

1. **Input Validation**: Validate all user inputs
2. **Token Security**: Store tokens securely using Capacitor Preferences
3. **HTTPS**: Always use HTTPS in production
4. **Error Messages**: Don't expose sensitive information in error messages

## Deployment

### Web Deployment

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

### Mobile Deployment

#### iOS App Store

1. Build in Xcode with Release configuration
2. Archive and upload to App Store Connect
3. Submit for review

#### Google Play Store

1. Build signed APK/AAB in Android Studio
2. Upload to Google Play Console
3. Submit for review

## Contributing

1. Follow the established code style and structure
2. Write tests for new functionality
3. Update documentation for significant changes
4. Test on both web and mobile platforms
5. Create descriptive commit messages
6. Submit pull requests for review
