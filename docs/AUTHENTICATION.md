# Authentication System

## Overview

The Agenda Familiar app uses Santillana Connect OAuth2 authentication with <PERSON><PERSON><PERSON> (Proof Key for Code Exchange) for secure authentication across web and mobile platforms.

## Architecture

### Core Components

1. **OAuth2 Provider**: Santillana Connect Identity Server
2. **Client Type**: Public client (no client secret)
3. **Flow**: Authorization Code with PKCE
4. **Mobile Integration**: Capacitor InAppBrowser for native platforms

### Authentication Flow

```mermaid
sequenceDiagram
    participant App as Mobile App
    participant IAB as InAppBrowser
    participant SC as Santillana Connect
    participant API as Backend API

    App->>IAB: Open authentication URL
    IAB->>SC: Redirect to login page
    User->>SC: Enter credentials
    SC->>IAB: Redirect with auth code
    IAB->>App: Return auth code
    App->>SC: Exchange code for tokens
    SC->>App: Return access/refresh tokens
    App->>API: Make authenticated requests
```

## Configuration

### Environment Variables

```bash
# Required environment variables
VITE_OIDC_AUTHORITY=https://pre-identity.santillanaconnect.com
VITE_OIDC_CLIENT_ID=sumun_office_co_pre
VITE_OIDC_SCOPE=openid profile email offline_access neds/full_access

# Optional debugging
VITE_DEBUG_AUTH=true
```

### Redirect URIs

- **Web**: `https://localhost:5173/callback`
- **Native**: `capacitor://localhost/callback`

## Mobile Platform Setup

### iOS Configuration

1. **URL Scheme Registration** (automatically handled by Capacitor)
2. **Deep Link Handling** in `capacitor.config.ts`:

```typescript
{
  plugins: {
    App: {
      deepLinkingEnabled: true
    }
  }
}
```

### Android Configuration

1. **Intent Filter** in `AndroidManifest.xml`:

```xml
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="capacitor" android:host="localhost" />
</intent-filter>
```

## Security Features

### Session Isolation

- Each authentication attempt starts with a clean browser state
- Prevents cached credential issues
- Ensures invalid credentials always fail

### Token Management

- Secure storage using Capacitor Preferences on native platforms
- Automatic token refresh with refresh tokens
- Proper token cleanup on logout

### PKCE Implementation

- Code challenge generated using SHA256
- State parameter for CSRF protection
- Secure random code verifier generation

## Usage

### Basic Authentication

```typescript
import { useAuth } from 'react-oidc-context';

function LoginComponent() {
  const auth = useAuth();

  const handleLogin = () => {
    auth.signinRedirect();
  };

  const handleLogout = () => {
    auth.signoutRedirect();
  };

  if (auth.isLoading) return <div>Loading...</div>;
  if (auth.error) return <div>Error: {auth.error.message}</div>;
  if (auth.isAuthenticated) return <div>Welcome {auth.user?.profile.name}</div>;

  return <button onClick={handleLogin}>Login</button>;
}
```

### Mobile Authentication

```typescript
import { AuthService } from '../services/auth.service';

// Smart authentication (seamless when possible)
const result = await AuthService.signIn();

// Forced fresh authentication
const result = await AuthService.signInForced();

// Silent authentication check
const user = await AuthService.getCurrentUser();
```

## Error Handling

### Common Errors

1. **Invalid Request**: Check redirect URI configuration
2. **Access Denied**: User cancelled authentication
3. **Network Errors**: Check connectivity and endpoints
4. **Token Expired**: Automatic refresh or re-authentication

### Error Recovery

```typescript
try {
  await auth.signinRedirect();
} catch (error) {
  if (error.error === 'access_denied') {
    // User cancelled - show friendly message
  } else if (error.error === 'invalid_request') {
    // Configuration issue - check setup
  } else {
    // Network or other error - retry logic
  }
}
```

## Testing

### Development Testing

1. Start development server: `npm run dev`
2. Navigate to login page
3. Test authentication flow
4. Verify token storage and refresh

### Mobile Testing

1. Build for platform: `npx cap build android/ios`
2. Run on device/emulator: `npx cap run android/ios`
3. Test deep link handling
4. Verify session isolation

### Automated Testing

```bash
# Run authentication tests
npm test -- auth

# Run E2E tests
npm run test:e2e
```

## Troubleshooting

### Common Issues

1. **Redirect URI Mismatch**: Ensure URIs match exactly in Santillana Connect
2. **CORS Errors**: Use InAppBrowser for mobile platforms
3. **Token Storage Issues**: Check Capacitor Preferences permissions
4. **Deep Link Failures**: Verify intent filters and URL schemes

### Debug Mode

Enable debug logging with `VITE_DEBUG_AUTH=true`:

```typescript
// Console output will show:
// 🔐 [AUTH] Authentication started
// 🔐 [AUTH] Token exchange successful
// 🔐 [AUTH] User profile loaded
```

## Security Considerations

1. **Never store client secrets** (public client)
2. **Use HTTPS** for all redirect URIs
3. **Validate tokens** on the backend
4. **Implement proper logout** to clear all sessions
5. **Use secure storage** for tokens on mobile
6. **Implement session timeouts** for security

## Migration Notes

If migrating from older authentication systems:

1. Update redirect URIs to use PKCE flow
2. Remove client secret references
3. Update token storage to use Capacitor Preferences
4. Test mobile deep link handling
5. Verify session isolation works correctly
