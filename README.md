# Agenda Familiar

A modern family agenda application built with React, Ionic, and Capacitor, featuring secure authentication through Santillana Connect.

## Features

- 🔐 **Secure Authentication**: OAuth2 with PKCE via Santillana Connect
- 📱 **Cross-Platform**: Web, iOS, and Android support
- 🌙 **Dark Mode**: Automatic and manual theme switching
- 👨‍👩‍👧‍👦 **Family Management**: Multi-student support with role-based access
- 📊 **Academic Tracking**: Attendance, grades, and curriculum progress
- 💬 **Communication**: Messages and notifications
- 📚 **Resource Catalog**: Educational materials and resources

## Quick Start

### Prerequisites

- Node.js 18+ and npm
- For mobile development:
  - iOS: Xcode 14+
  - Android: Android Studio with SDK 33+

### Installation

```bash
# Clone and install dependencies
git clone <repository-url>
cd agenda-familiar
npm install

# Sync Capacitor dependencies
npx cap sync
```

### Development

```bash
# Start development server
npm run dev
# App available at https://localhost:5173

# Mobile development
npx cap run ios     # iOS
npx cap run android # Android
```

### Docker Development

```bash
# Development with hot-reload
docker compose --profile dev up --build

# Pre-production build
docker compose --profile pre up --build

# Production build
docker compose --profile prod up --build
```

## Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── common/          # Generic components (ErrorBoundary, LoadingButton)
│   ├── layout/          # Layout components (GlobalHeader, Navigation)
│   └── features/        # Feature-specific components
├── pages/               # Page components (Auth, Home, Account)
├── services/            # Business logic services
├── hooks/               # Custom React hooks
├── contexts/            # React contexts (Auth, Theme, User)
├── config/              # Configuration files
├── utils/               # Utility functions
├── types/               # TypeScript type definitions
└── styles/              # Global styles
```

## Architecture

### Core Principles

- **Component-Based**: Modular, reusable UI components
- **Service-Oriented**: Business logic separated into services
- **Context-Driven**: State management via React Context
- **Type-Safe**: Full TypeScript implementation
- **Mobile-First**: Capacitor for native mobile features

### Key Technologies

- **Frontend**: React 18, TypeScript, Ionic Framework
- **Mobile**: Capacitor for iOS/Android
- **Authentication**: OAuth2 PKCE with Santillana Connect
- **Styling**: Ionic CSS Variables, Tailwind CSS
- **State Management**: React Context + Hooks
- **Testing**: Vitest, Cypress
- **Build**: Vite

## Documentation

- [Authentication Guide](./docs/AUTHENTICATION.md) - Complete authentication setup and usage
- [Development Guide](./docs/DEVELOPMENT.md) - Development workflow and best practices
- [API Documentation](./docs/API.md) - API endpoints and integration
- [Deployment Guide](./docs/DEPLOYMENT.md) - Production deployment instructions

## Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build

# Testing
npm test             # Run unit tests
npm run test:e2e     # Run E2E tests
npm run lint         # Run ESLint

# Mobile
npx cap sync         # Sync Capacitor dependencies
npx cap run ios      # Run on iOS
npx cap run android  # Run on Android
```

## Environment Variables

```bash
# Required
VITE_OIDC_AUTHORITY=https://pre-identity.santillanaconnect.com
VITE_OIDC_CLIENT_ID=sumun_office_co_pre
VITE_OIDC_SCOPE=openid profile email offline_access neds/full_access

# Optional
VITE_DEBUG_AUTH=true  # Enable authentication debugging
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/feature-name`
3. Make your changes following the project structure
4. Add tests for new functionality
5. Test on both web and mobile platforms
6. Submit a pull request

## License

This project is proprietary software developed for Santillana.

