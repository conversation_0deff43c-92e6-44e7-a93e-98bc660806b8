version: "3.9"

services:
  # Development environment
  agenda-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    volumes:
      # Live code sync for development
      - .:/app
      - /app/node_modules
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_DEBUG_AUTH=true
    profiles:
      - dev
      - development

  # Pre-production environment
  agenda-pre:
    build:
      context: .
      dockerfile: Dockerfile
      target: pre-production
    ports:
      - "4173:4173"
    environment:
      - NODE_ENV=pre
      - VITE_API_URL=https://pre-api.yourdomain.com
    profiles:
      - pre
      - preproduction

  # Production environment
  agenda-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    profiles:
      - prod
      - production

# Usage examples:
# Development: docker compose --profile dev up --build
# Pre-production: docker compose --profile pre up --build  
# Production: docker compose --profile prod up --build
