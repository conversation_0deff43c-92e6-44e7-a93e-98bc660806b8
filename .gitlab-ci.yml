stages:
  - install
  - build
  - test

variables:
  NODE_VERSION: "24.1.0"

cache:
  paths:
    - node_modules/

# Install dependencies
install:
  stage: install
  image: node:${NODE_VERSION}
  script:
    - npm ci
  artifacts:
    paths:
      - node_modules/
    expire_in: 1 hour

# Build application
build:
  stage: build
  image: node:${NODE_VERSION}
  dependencies:
    - install
  script:
    - npm run build
  artifacts:
    paths:
      - dist/
    expire_in: 1 week

# Run tests and linting
test:
  stage: test
  image: node:${NODE_VERSION}
  dependencies:
    - install
  script:
    - npm run lint
  allow_failure: false